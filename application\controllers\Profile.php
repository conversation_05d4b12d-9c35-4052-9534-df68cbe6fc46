<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Superadmins $superadmins
 * @property Bumdes_user_model $bumdes_users
 * @property CI_Form_validation $form_validation
 */
class Profile extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Superadmins', 'superadmins');
        $this->load->model('Bumdes_user_model', 'bumdes_users');
        $this->load->helper('security');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Profil Pengguna';
        $data['content'] = 'profile/index';

        // Get user data based on role
        if (isBumdesUser()) {
            // For BUMDes User, get from msbumdesusers table
            $data['user'] = $this->bumdes_users->getUsersWithDetails(array('a.id' => getCurrentIdUser()))->row();
        } else {
            // For Super Admin, BUMDes, get from msusers table
            $data['user'] = getCurrentUser();
        }

        return $this->load->view('master', $data);
    }

    public function change_password()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Ubah Password';
        $data['content'] = 'profile/change_password';

        return $this->load->view('master', $data);
    }

    public function process_change_password()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $current_password = getPost('current_password');
        $new_password = getPost('new_password');
        $confirm_password = getPost('confirm_password');

        // Validasi input
        $this->form_validation->set_rules('current_password', 'Password Saat Ini', 'required|trim');
        $this->form_validation->set_rules('new_password', 'Password Baru', 'required|trim|min_length[6]');
        $this->form_validation->set_rules('confirm_password', 'Konfirmasi Password', 'required|trim|matches[new_password]');

        if ($this->form_validation->run() == false) {
            return JSONResponseDefault('FAILED', strip_tags(validation_errors()));
        }

        $user_id = getCurrentIdUser();
        $current_user = null;

        // Get current user data based on role
        if (isBumdesUser()) {
            // For BUMDes User, get from msbumdesusers table
            $current_user = $this->bumdes_users->get(array('id' => $user_id))->row();
            $table = 'msbumdesusers';
        } else {
            // For Super Admin, BUMDes, get from msusers table
            $current_user = $this->superadmins->get(array('id' => $user_id))->row();
            $table = 'msusers';
        }

        if (!$current_user) {
            return JSONResponseDefault('FAILED', 'Data pengguna tidak ditemukan.');
        }

        // Verify current password
        if (!password_verify($current_password, $current_user->password)) {
            // Log security event for failed password change attempt
            logSecurityEvent('PASSWORD_CHANGE_FAILED', [
                'user_id' => $user_id,
                'username' => $current_user->username ?? $current_user->name,
                'reason' => 'Invalid current password'
            ], 'MEDIUM', $user_id);

            return JSONResponseDefault('FAILED', 'Password saat ini tidak sesuai.');
        }

        // Hash new password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

        // Update password in database
        $this->db->trans_begin();

        try {
            $update_data = array(
                'password' => $hashed_password,
                'updateddate' => getCurrentDate(),
                'updatedby' => $user_id
            );

            $this->db->where('id', $user_id)->update($table, $update_data);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Database update failed');
            }

            $this->db->trans_commit();

            // Log successful password change
            logSecurityEvent('PASSWORD_CHANGED', [
                'user_id' => $user_id,
                'username' => $current_user->username ?? $current_user->name,
                'user_type' => getSessionValue('ROLE')
            ], 'LOW', $user_id);

            return JSONResponseDefault('OK', 'Password berhasil diubah.');
        } catch (Exception $e) {
            $this->db->trans_rollback();

            // Log error
            logSecurityEvent('PASSWORD_CHANGE_ERROR', [
                'user_id' => $user_id,
                'username' => $current_user->username ?? $current_user->name,
                'error' => $e->getMessage()
            ], 'HIGH', $user_id);

            return JSONResponseDefault('FAILED', 'Terjadi kesalahan saat mengubah password. Silakan coba lagi.');
        }
    }

    public function update_profile()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $name = getPost('name');
        $phone = getPost('phone');
        $address = getPost('address');

        // Validasi input
        $this->form_validation->set_rules('name', 'Nama', 'required|trim');

        if ($this->form_validation->run() == false) {
            return JSONResponseDefault('FAILED', strip_tags(validation_errors()));
        }

        $user_id = getCurrentIdUser();

        // Update profile based on role
        $this->db->trans_begin();

        try {
            if (isBumdesUser()) {
                // For BUMDes User, update msbumdesusers table
                $update_data = array(
                    'name' => $name,
                    'phone' => $phone,
                    'address' => $address,
                    'updateddate' => getCurrentDate(),
                    'updatedby' => $user_id
                );

                $this->db->where('id', $user_id)->update('msbumdesusers', $update_data);
            } else {
                // For Super Admin, BUMDes, update msusers table
                $update_data = array(
                    'name' => $name,
                    'updateddate' => getCurrentDate(),
                    'updatedby' => $user_id
                );

                // Add phone and address if fields exist in msusers table
                if ($this->db->field_exists('phone', 'msusers')) {
                    $update_data['phone'] = $phone;
                }
                if ($this->db->field_exists('address', 'msusers')) {
                    $update_data['address'] = $address;
                }

                $this->db->where('id', $user_id)->update('msusers', $update_data);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Database update failed');
            }

            $this->db->trans_commit();

            // Update session name if changed
            if ($name !== getSessionValue('NAME')) {
                setSessionValue(array('NAME' => $name));
            }

            return JSONResponseDefault('OK', 'Profil berhasil diperbarui.');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Terjadi kesalahan saat memperbarui profil. Silakan coba lagi.');
        }
    }

    public function update_business_info()
    {
        if (!isLogin() || !isBumdes()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $business_owner = getPost('business_owner');
        $business_name = getPost('business_name');

        // Validasi input
        $this->form_validation->set_rules('business_owner', 'Nama Pemilik Usaha', 'required|trim');
        $this->form_validation->set_rules('business_name', 'Nama Usaha', 'required|trim');

        if ($this->form_validation->run() == false) {
            return JSONResponseDefault('FAILED', strip_tags(validation_errors()));
        }

        $user_id = getCurrentIdUser();

        // Update business info in msusers table
        $this->db->trans_begin();

        try {
            $update_data = array(
                'name' => $business_owner,
                'businessname' => $business_name,
                'updateddate' => getCurrentDate(),
                'updatedby' => $user_id
            );

            $this->db->where('id', $user_id)->update('msusers', $update_data);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Database update failed');
            }

            $this->db->trans_commit();

            // Update session name if changed
            if ($business_owner !== getSessionValue('NAME')) {
                setSessionValue(array('NAME' => $business_owner));
            }

            return JSONResponseDefault('OK', 'Informasi usaha berhasil diperbarui.');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', 'Terjadi kesalahan saat memperbarui informasi usaha. Silakan coba lagi.');
        }
    }
}
