<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Monitoring <PERSON><PERSON><PERSON></h6>
    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">Super Admin</li>
    </ul>
</div>

<div class="row row-cols-xxl-4 row-cols-lg-2 row-cols-sm-2 row-cols-1 gy-4">
    <div class="col">
        <div class="card shadow-none border bg-gradient-start-4 h-100">
            <div class="card-body p-20">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                    <div>
                        <p class="fw-medium text-primary-light mb-1">Total Pendapatan</p>
                        <h6 class="mb-0">Rp <?= IDR($total_income_all) ?></h6>
                    </div>
                    <div class="w-50-px h-50-px bg-success-main rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:wallet-money-bold" class="text-white text-2xl mb-0"></iconify-icon>
                    </div>
                </div>
                <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                    <span class="d-inline-flex align-items-center gap-1 text-success-main">
                        <iconify-icon icon="bxs:up-arrow" class="text-xs"></iconify-icon> +<?= getSelectedYear() ?>
                    </span>
                    Semua Desa Tahun <?= getSelectedYear() ?>
                </p>
            </div>
        </div>
    </div>

    <div class="col">
        <div class="card shadow-none border bg-gradient-start-5 h-100">
            <div class="card-body p-20">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                    <div>
                        <p class="fw-medium text-primary-light mb-1">Total Pengeluaran</p>
                        <h6 class="mb-0">Rp <?= IDR($total_expense_all) ?></h6>
                    </div>
                    <div class="w-50-px h-50-px bg-red rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:card-send-bold" class="text-white text-2xl mb-0"></iconify-icon>
                    </div>
                </div>
                <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                    <span class="d-inline-flex align-items-center gap-1 text-danger-main">
                        <iconify-icon icon="bxs:down-arrow" class="text-xs"></iconify-icon> -<?= getSelectedYear() ?>
                    </span>
                    Semua Desa Tahun <?= getSelectedYear() ?>
                </p>
            </div>
        </div>
    </div>

    <div class="col">
        <div class="card shadow-none border bg-gradient-start-3 h-100">
            <div class="card-body p-20">
                <?php $net_income = $total_income_all - $total_expense_all; ?>
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                    <div>
                        <p class="fw-medium text-primary-light mb-1">Laba Bersih</p>
                        <h6 class="mb-0">Rp <?= IDR(abs($net_income)) ?></h6>
                    </div>
                    <div class="w-50-px h-50-px bg-<?= $net_income >= 0 ? 'info' : 'warning' ?> rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:chart-square-bold" class="text-white text-2xl mb-0"></iconify-icon>
                    </div>
                </div>
                <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                    <span class="d-inline-flex align-items-center gap-1 text-<?= $net_income >= 0 ? 'success' : 'danger' ?>-main">
                        <iconify-icon icon="bxs:<?= $net_income >= 0 ? 'up' : 'down' ?>-arrow" class="text-xs"></iconify-icon>
                        <?= $net_income >= 0 ? 'Profit' : 'Loss' ?>
                    </span>
                    Semua Desa Tahun <?= getSelectedYear() ?>
                </p>
            </div>
        </div>
    </div>

    <div class="col">
        <div class="card shadow-none border bg-gradient-start-1 h-100">
            <div class="card-body p-20">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                    <div>
                        <p class="fw-medium text-primary-light mb-1">Total BUMDes</p>
                        <h6 class="mb-0"><?= $total_bumdes ?></h6>
                    </div>
                    <div class="w-50-px h-50-px bg-cyan rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:buildings-bold" class="text-white text-2xl mb-0"></iconify-icon>
                    </div>
                </div>
                <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                    <span class="d-inline-flex align-items-center gap-1 text-success-main">
                        <iconify-icon icon="bxs:up-arrow" class="text-xs"></iconify-icon> Active
                    </span>
                    Terdaftar dalam Sistem
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row gy-4 mt-1">
    <div class="col-xxl-6 col-xl-12">
        <div class="card h-100">
            <div class="card-body">
                <div class="d-flex flex-wrap align-items-center justify-content-between">
                    <h6 class="text-lg mb-0">Grafik Keuangan Bulanan</h6>
                </div>
                <div class="d-flex flex-wrap align-items-center gap-2 mt-8">
                    <h6 class="mb-0">Rp <?= IDR($total_income_all) ?></h6>
                    <span class="text-sm fw-semibold rounded-pill bg-success-focus text-success-main border br-success px-8 py-4 line-height-1 d-flex align-items-center gap-1">
                        <?php
                        $growth = $total_income_all > 0 ? (($total_income_all - $total_expense_all) / $total_income_all) * 100 : 0;
                        echo number_format(abs($growth), 1) . '%';
                        ?>
                        <iconify-icon icon="bxs:<?= $growth >= 0 ? 'up' : 'down' ?>-arrow" class="text-xs"></iconify-icon>
                    </span>
                    <span class="text-xs fw-medium">+ Rp <?= IDR($total_income_all / 12) ?> Per Bulan</span>
                </div>
                <div id="chart" class="pt-28 apexcharts-tooltip-style-1"></div>
            </div>
        </div>
    </div>

    <div class="col-xxl-3 col-xl-6">
        <div class="card radius-8 border">
            <div class="card-body p-24">
                <h6 class="mb-12 fw-semibold text-lg mb-16">Statistik Cepat</h6>
                <div class="d-flex align-items-center gap-2 mb-20">
                    <h6 class="fw-semibold mb-0">Rp <?= IDR($total_income_all / 12) ?></h6>
                    <p class="text-sm mb-0">
                        <span class="bg-success-focus border br-success px-8 py-2 rounded-pill fw-semibold text-success-main text-sm d-inline-flex align-items-center gap-1">
                            <?php
                            $monthly_growth = 10; // Placeholder untuk growth rate
                            echo $monthly_growth . '%';
                            ?>
                            <iconify-icon icon="iconamoon:arrow-up-2-fill" class="icon"></iconify-icon>
                        </span>
                        + Rata-rata Per Bulan
                    </p>
                </div>

                <div class="mt-3">
                    <div class="d-flex align-items-center justify-content-between mb-16">
                        <span class="text-sm fw-medium text-secondary-light">Rata-rata Pendapatan/Bulan</span>
                        <span class="text-sm fw-semibold text-success-main">Rp <?= IDR($total_income_all / 12) ?></span>
                    </div>
                    <div class="d-flex align-items-center justify-content-between mb-16">
                        <span class="text-sm fw-medium text-secondary-light">Rata-rata Pengeluaran/Bulan</span>
                        <span class="text-sm fw-semibold text-danger-main">Rp <?= IDR($total_expense_all / 12) ?></span>
                    </div>
                    <div class="d-flex align-items-center justify-content-between mb-16">
                        <span class="text-sm fw-medium text-secondary-light">Total Transaksi</span>
                        <span class="text-sm fw-semibold text-info-main"><?= $total_transactions_all ?></span>
                    </div>
                    <div class="d-flex align-items-center justify-content-between">
                        <span class="text-sm fw-medium text-secondary-light">Efisiensi Keuangan</span>
                        <span class="text-sm fw-semibold text-primary-main">
                            <?php
                            $efficiency = $total_income_all > 0 ? (($total_income_all - $total_expense_all) / $total_income_all) * 100 : 0;
                            echo number_format($efficiency, 1) . '%';
                            ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xxl-3 col-xl-6">
        <div class="card shadow-none border bg-gradient-start-4 mb-3">
            <div class="card-body p-20">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                    <div>
                        <p class="fw-medium text-primary-light mb-1">Total Modal BUMDes</p>
                        <h6 class="mb-0">Rp <?= IDR($total_modal_bumdes) ?></h6>
                    </div>
                    <div class="w-50-px h-50-px bg-purple rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:wallet-money-bold" class="text-white text-2xl mb-0"></iconify-icon>
                    </div>
                </div>
                <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                    <span class="d-inline-flex align-items-center gap-1 text-info-main">
                        <iconify-icon icon="solar:money-bag-bold" class="text-xs"></iconify-icon> Capital
                    </span>
                    Keseluruhan Tahun <?= getSelectedYear() ?>
                </p>
            </div>
        </div>

        <div class="card radius-8 border-0 overflow-hidden">
            <div class="card-body p-24">
                <div class="d-flex align-items-center flex-wrap gap-2 justify-content-between">
                    <h6 class="mb-2 fw-bold text-lg">BUMDes Overview</h6>
                </div>

                <div class="mt-3">
                    <div class="d-flex align-items-center justify-content-between mb-16">
                        <span class="text-sm fw-medium text-secondary-light">Total BUMDes Aktif</span>
                        <span class="text-sm fw-semibold text-success-main"><?= $total_bumdes ?></span>
                    </div>
                    <div class="d-flex align-items-center justify-content-between mb-16">
                        <span class="text-sm fw-medium text-secondary-light">BUMDes Profit</span>
                        <span class="text-sm fw-semibold text-success-main">
                            <?php
                            $profit_count = 0;
                            if (!empty($financial_summary)) {
                                foreach ($financial_summary as $summary) {
                                    if ($summary->net_income > 0) $profit_count++;
                                }
                            }
                            echo $profit_count;
                            ?>
                        </span>
                    </div>
                    <div class="d-flex align-items-center justify-content-between">
                        <span class="text-sm fw-medium text-secondary-light">BUMDes Loss</span>
                        <span class="text-sm fw-semibold text-danger-main">
                            <?php
                            $loss_count = 0;
                            if (!empty($financial_summary)) {
                                foreach ($financial_summary as $summary) {
                                    if ($summary->net_income < 0) $loss_count++;
                                }
                            }
                            echo $loss_count;
                            ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row gy-4 mt-1">
    <div class="col-xxl-12">
        <div class="card basic-data-table">
            <div class="card-header">
                <h5 class="card-title mb-0">Monitoring Keuangan per Desa/BUMDes</h5>
            </div>
            <div class="card-body">
                <table class="table bordered-table mb-0 datatables" id="dataTable" data-page-length='10'>
                    <thead>
                        <tr>
                            <th scope="col">Nama BUMDes</th>
                            <th scope="col">Desa</th>
                            <th scope="col">Kecamatan</th>
                            <th scope="col">Kab/Kota</th>
                            <th scope="col">Pendapatan</th>
                            <th scope="col">Pengeluaran</th>
                            <th scope="col">Laba Bersih</th>
                            <th scope="col">Transaksi</th>
                            <th scope="col">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($financial_summary)): ?>
                            <?php foreach ($financial_summary as $index => $summary): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="w-40-px h-40-px bg-primary-100 text-primary-600 rounded-circle d-flex justify-content-center align-items-center flex-shrink-0 me-12">
                                                <iconify-icon icon="solar:buildings-2-bold" class="text-lg"></iconify-icon>
                                            </div>
                                            <h6 class="text-md mb-0 fw-medium flex-grow-1"><?= $summary->bumdes_name ?></h6>
                                        </div>
                                    </td>
                                    <td><?= $summary->village_name ?? '-' ?></td>
                                    <td><?= $summary->district_name ?? '-' ?></td>
                                    <td><?= $summary->city_name ?? '-' ?></td>
                                    <td>Rp <?= IDR($summary->total_income) ?></td>
                                    <td>Rp <?= IDR($summary->total_expense) ?></td>
                                    <td><?= $summary->net_income >= 0 ? '+' : '' ?>Rp <?= IDR(abs($summary->net_income)) ?></td>
                                    <td><?= $summary->transaction_count ?></td>
                                    <td>
                                        <?php if ($summary->net_income > 0): ?>
                                            <span class="bg-success-focus text-success-main px-24 py-4 rounded-pill fw-medium text-sm">Untung</span>
                                        <?php elseif ($summary->net_income < 0): ?>
                                            <span class="bg-danger-focus text-danger-main px-24 py-4 rounded-pill fw-medium text-sm">Rugi</span>
                                        <?php else: ?>
                                            <span class="bg-warning-focus text-warning-main px-24 py-4 rounded-pill fw-medium text-sm">Impas</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="9" class="text-center py-5">
                                    <div class="d-flex flex-column align-items-center">
                                        <iconify-icon icon="solar:database-bold" class="text-6xl text-secondary-light mb-3"></iconify-icon>
                                        <h6 class="text-secondary-light mb-1">Belum Ada Data Keuangan</h6>
                                        <p class="text-sm text-secondary-light mb-0">Data keuangan akan muncul setelah ada transaksi dari BUMDes</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row gy-4 mt-1">
    <div class="col-xxl-12">
        <div class="card basic-data-table">
            <div class="card-header">
                <h5 class="card-title mb-0">Transaksi Terbaru</h5>
            </div>
            <div class="card-body">
                <table class="table bordered-table mb-0" id="dataTable" data-page-length='10'>
                    <thead>
                        <tr>
                            <th scope="col">Kode Transaksi</th>
                            <th scope="col">BUMDes</th>
                            <th scope="col">Desa</th>
                            <th scope="col">Tanggal</th>
                            <th scope="col">Catatan</th>
                            <th scope="col">Jenis</th>
                            <th scope="col">Unit Usaha</th>
                            <th scope="col">Nominal</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($recent_transactions)): ?>
                            <?php foreach ($recent_transactions as $transaction): ?>
                                <tr>
                                    <td><a href="javascript:void(0)" class="text-primary-600"><?= $transaction->transactioncode ?></a></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="w-32-px h-32-px bg-primary-100 text-primary-600 rounded-circle d-flex justify-content-center align-items-center flex-shrink-0 me-12">
                                                <iconify-icon icon="solar:buildings-2-bold" class="text-sm"></iconify-icon>
                                            </div>
                                            <h6 class="text-md mb-0 fw-medium flex-grow-1"><?= $transaction->bumdes_name ?? 'BUMDes' ?></h6>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-sm text-secondary-light"><?= $transaction->village_name ?? '-' ?></span>
                                    </td>
                                    <td><?= tgl_indo(date('Y-m-d', strtotime($transaction->transactiondate))) ?></td>
                                    <td title="<?= $transaction->transactionnote ?>">
                                        <?= strlen($transaction->transactionnote) > 30 ? substr($transaction->transactionnote, 0, 30) . '...' : $transaction->transactionnote ?>
                                    </td>
                                    <td>
                                        <?php if ($transaction->transactiontype == 'Pendapatan'): ?>
                                            <span class="bg-success-focus text-success-main px-24 py-4 rounded-pill fw-medium text-sm">Pendapatan</span>
                                        <?php else: ?>
                                            <span class="bg-danger-focus text-danger-main px-24 py-4 rounded-pill fw-medium text-sm">Pengeluaran</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="text-sm text-secondary-light"><?= isset($transaction->workunitdata) ? $transaction->workunitdata : 'N/A' ?></span>
                                    </td>
                                    <td><?= $transaction->transactiontype == 'Pendapatan' ? '+' : '-' ?>Rp <?= number_format($transaction->amount, 0, ',', '.') ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <div class="d-flex flex-column align-items-center">
                                        <iconify-icon icon="solar:document-bold" class="text-6xl text-secondary-light mb-3"></iconify-icon>
                                        <h6 class="text-secondary-light mb-1">Belum Ada Transaksi</h6>
                                        <p class="text-sm text-secondary-light mb-0">Transaksi akan muncul setelah ada aktivitas keuangan dari BUMDes</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    // Flag to prevent multiple chart initialization
    var superAdminChartInitialized = false;

    // Function to initialize Super Admin dashboard chart
    function initializeSuperAdminChart() {
        // Prevent multiple initialization
        if (superAdminChartInitialized) {
            console.log('Super Admin chart already initialized, skipping...');
            return;
        }
        var options = {
            series: [{
                name: 'Pendapatan',
                data: <?= $chart_revenue ?>
            }, {
                name: 'Pengeluaran',
                data: <?= $chart_expense ?>
            }],
            chart: {
                type: 'area',
                height: 350,
                toolbar: {
                    show: false
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth',
                width: 2
            },
            xaxis: {
                categories: <?= $months ?>
            },
            yaxis: {
                labels: {
                    formatter: function(val) {
                        return "Rp " + val.toLocaleString('id-ID');
                    }
                }
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.9,
                    stops: [0, 100]
                }
            },
            colors: ['#10B981', '#EF4444'],
            tooltip: {
                y: {
                    formatter: function(val) {
                        return "Rp " + val.toLocaleString('id-ID');
                    }
                }
            }
        };

        // Check if chart element exists before creating chart
        var chartElement = document.querySelector("#chart");
        if (chartElement) {
            // Check if ApexCharts is available
            if (typeof ApexCharts !== 'undefined') {
                var chart = new ApexCharts(chartElement, options);
                chart.render();
                superAdminChartInitialized = true; // Mark as initialized
                console.log('Super Admin chart initialized successfully');
            } else {
                // Queue for later execution if ApexCharts not loaded yet
                console.log('ApexCharts not loaded yet, queuing Super Admin chart');
                window.ApexChartsQueue = window.ApexChartsQueue || [];
                window.ApexChartsQueue.push({
                    element: chartElement,
                    options: options,
                    timestamp: Date.now()
                });
                superAdminChartInitialized = true; // Mark as initialized to prevent multiple queuing
            }
        } else {
            console.warn('Chart element #chart not found');
        }
    }

    // Function to initialize DataTables
    function initializeDataTables() {
        // Check if jQuery and DataTables are available
        if (typeof $ !== 'undefined' && $.fn.DataTable) {
            try {
                // Destroy existing DataTables if they exist
                if ($.fn.DataTable.isDataTable('#dataTable')) {
                    $('#dataTable').DataTable().destroy();
                }

                // Also check for any other potential DataTable instances
                $('.datatables').each(function() {
                    if ($.fn.DataTable.isDataTable(this)) {
                        $(this).DataTable().destroy();
                    }
                });

                // Initialize monitoring table
                $('#dataTable').DataTable({
                    "pageLength": 10,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "paging": true,
                    "responsive": true,
                    "columnDefs": [{
                            "orderable": false,
                            "targets": [8] // Status column not sortable
                        },
                        {
                            "className": "text-center",
                            "targets": [4, 5, 6, 7, 8] // Center align financial columns
                        }
                    ],
                    "order": [
                        [0, 'asc']
                    ] // Sort by BUMDes name ascending
                });

                console.log('DataTables initialized successfully');
            } catch (error) {
                console.error('Error initializing DataTables:', error);
            }
        } else {
            console.warn('jQuery or DataTables not available yet');
        }
    }

    // Wait for DOM and scripts to be ready
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            initializeSuperAdminChart();
            initializeDataTables();
        }, 500);
    });

    // Fallback: Also try when window loads
    window.onload = function() {
        setTimeout(function() {
            // Only initialize if not already done
            if (!superAdminChartInitialized) {
                initializeSuperAdminChart();
            }
            // Always try to initialize DataTables
            initializeDataTables();
        }, 800);
    };

    // Additional fallback for jQuery and ApexCharts loading
    function checkLibrariesAndInit() {
        // Check if jQuery is available
        if (typeof $ !== 'undefined') {
            // Use jQuery ready
            $(document).ready(function() {
                setTimeout(function() {
                    initializeDataTables();
                }, 300);
            });

            // Check periodically if ApexCharts is loaded
            var checkApexCharts = setInterval(function() {
                if (typeof ApexCharts !== 'undefined') {
                    clearInterval(checkApexCharts);
                    setTimeout(function() {
                        // Only initialize if not already done
                        if (!superAdminChartInitialized) {
                            initializeSuperAdminChart();
                        }
                    }, 100);
                }
            }, 100);

            // Stop checking after 10 seconds
            setTimeout(function() {
                clearInterval(checkApexCharts);
            }, 10000);
        } else {
            // jQuery not available yet, try again later
            setTimeout(checkLibrariesAndInit, 200);
        }
    }

    // Start checking for libraries
    setTimeout(checkLibrariesAndInit, 100);
</script>

<!-- Custom CSS untuk styling tambahan -->
<style>
    /* WowDash Gradient Backgrounds */
    .bg-gradient-start-1 {
        background: linear-gradient(to right, #E6F9FF, #FEFFFF);
    }

    .bg-gradient-start-2 {
        background: linear-gradient(to right, #F7E9FF, #FFFEFD);
    }

    .bg-gradient-start-3 {
        background: linear-gradient(to right, #E6EBFF, #FFFFFF);
    }

    .bg-gradient-start-4 {
        background: linear-gradient(to right, #E8FFF5, #FFFFFF);
    }

    .bg-gradient-start-5 {
        background: linear-gradient(to right, #FFEEEE, #FFFCFC);
    }

    /* Enhanced Card Styling */
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
    }

    /* Chart Container */
    .chart-container {
        min-height: 350px;
    }

    .apexcharts-tooltip-style-1 {
        min-height: 350px;
    }

    /* WowDash Table Styles */
    .basic-data-table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .basic-data-table .card-header {
        background-color: #fff;
        border-bottom: 1px solid #E2E8F0;
        padding: 20px 24px;
    }

    .basic-data-table .card-title {
        font-size: 18px;
        font-weight: 600;
        color: #1E293B;
    }

    .basic-data-table .table.bordered-table {
        border: 1px solid #E2E8F0;
        margin-bottom: 0;
    }

    .basic-data-table .table.bordered-table th,
    .basic-data-table .table.bordered-table td {
        border: 1px solid #E2E8F0;
        padding: 12px 16px;
        vertical-align: middle;
    }

    .basic-data-table .table.bordered-table thead th {
        background-color: #F8FAFC;
        font-weight: 600;
        color: #475569;
        border-bottom: 2px solid #E2E8F0;
        font-size: 14px;
    }

    .basic-data-table .table.bordered-table tbody tr:hover {
        background-color: #F8FAFC;
    }

    .basic-data-table .table.bordered-table tbody td {
        font-size: 14px;
        color: #374151;
    }

    /* WowDash Utility Classes */
    .w-32-px {
        width: 32px;
    }

    .h-32-px {
        height: 32px;
    }

    .w-40-px {
        width: 40px;
    }

    .h-40-px {
        height: 40px;
    }

    .me-12 {
        margin-right: 12px;
    }

    .px-24 {
        padding-left: 24px;
        padding-right: 24px;
    }

    .py-4 {
        padding-top: 4px;
        padding-bottom: 4px;
    }

    .radius-8 {
        border-radius: 8px;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .card-body {
            padding: 1rem !important;
        }

        .apexcharts-tooltip-style-1 {
            min-height: 280px;
        }
    }
</style>