<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Transaction_status_history extends MY_Model
{
    protected $table = 'transaction_status_history';

    /**
     * Log transaction status change
     */
    public function logStatusChange($transaction_id, $old_status, $new_status, $changed_by, $notes = null)
    {
        $data = array(
            'transaction_id' => $transaction_id,
            'old_status' => $old_status,
            'new_status' => $new_status,
            'changed_by' => $changed_by,
            'changed_date' => getCurrentDate(),
            'notes' => $notes
        );

        return $this->insert($data);
    }

    /**
     * Get status history for a transaction
     */
    public function getTransactionHistory($transaction_id)
    {
        return $this->db->select('h.*, u.name as changed_by_name')
            ->from($this->table . ' h')
            ->join('msusers u', 'h.changed_by = u.id', 'left')
            ->where('h.transaction_id', $transaction_id)
            ->order_by('h.changed_date', 'DESC')
            ->get()
            ->result();
    }
}
