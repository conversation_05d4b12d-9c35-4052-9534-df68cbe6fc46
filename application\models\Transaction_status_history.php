<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Transaction_status_history extends MY_Model
{
    protected $table = 'transaction_status_history';

    /**
     * Log transaction status change
     */
    public function logStatusChange($transaction_id, $old_status, $new_status, $changed_by, $notes = null)
    {
        // Check if table exists first
        if (!$this->db->table_exists($this->table)) {
            return false; // Skip logging if table doesn't exist
        }

        $data = array(
            'transaction_id' => $transaction_id,
            'old_status' => $old_status,
            'new_status' => $new_status,
            'changed_by' => $changed_by,
            'changed_date' => getCurrentDate(),
            'notes' => $notes
        );

        return $this->insert($data);
    }

    /**
     * Get status history for a transaction
     */
    public function getTransactionHistory($transaction_id)
    {
        // Check if table exists first
        if (!$this->db->table_exists($this->table)) {
            return array(); // Return empty array if table doesn't exist
        }

        return $this->db->select('h.*, u.name as changed_by_name, u.role as changed_by_role')
            ->from($this->table . ' h')
            ->join('msusers u', 'h.changed_by = u.id', 'left')
            ->where('h.transaction_id', $transaction_id)
            ->order_by('h.changed_date', 'DESC')
            ->get()
            ->result();
    }

    /**
     * Get history perubahan status dengan filter
     */
    public function getHistoryWithFilters($filters = array(), $limit = null, $offset = null)
    {
        // Check if table exists first
        if (!$this->db->table_exists($this->table)) {
            return $this->db->get_where('transactions', '1=0'); // Return empty result
        }

        $this->db->select('h.*, t.transactioncode, t.transactiondate, t.amount, t.transactionnote,
                          u.name as changed_by_name, u.role as changed_by_role,
                          w.workunitname')
            ->from($this->table . ' h')
            ->join('transactions t', 'h.transaction_id = t.id', 'left')
            ->join('msusers u', 'h.changed_by = u.id', 'left')
            ->join('msworkunits w', 't.workunitid = w.id', 'left');

        // Apply filters based on user role
        if (isset($filters['user_role'])) {
            if ($filters['user_role'] === 'BUMDes') {
                // BUMDes hanya melihat transaksi dari desa mereka
                $village_id = getVillageId();
                $current_user_id = getCurrentIdUser();

                if ($village_id) {
                    // Get all BUMDes users from the same village using separate query
                    $CI = &get_instance();
                    $village_bumdes_users = array();

                    // Use separate database query to avoid conflict
                    $bumdes_users_query = $CI->db->select('a.id')
                        ->from('msbumdesusers a')
                        ->join('msusers b', 'a.bumdes_id = b.id', 'inner')
                        ->where('b.villageid', $village_id)
                        ->get();

                    foreach ($bumdes_users_query->result() as $user) {
                        $village_bumdes_users[] = $user->id;
                    }

                    // Include current BUMDes and all BUMDes users from same village
                    $allowed_creators = array_merge(array($current_user_id), $village_bumdes_users);
                    $this->db->where_in('t.createdby', $allowed_creators);
                } else {
                    $this->db->where('t.createdby', $current_user_id);
                }
            } elseif ($filters['user_role'] === 'BUMDes User') {
                // BUMDes User hanya melihat transaksi dari unit kerja mereka
                $this->db->where('t.workunitid', getWorkunitId());
                $this->db->where('t.createdby', getBumdesId());
            }
        }

        // Date range filter
        if (isset($filters['date_from']) && !empty($filters['date_from'])) {
            $this->db->where('DATE(h.changed_date) >=', $filters['date_from']);
        }
        if (isset($filters['date_to']) && !empty($filters['date_to'])) {
            $this->db->where('DATE(h.changed_date) <=', $filters['date_to']);
        }

        // Status filter
        if (isset($filters['status']) && !empty($filters['status'])) {
            $this->db->where('h.new_status', $filters['status']);
        }

        // Transaction type filter
        if (isset($filters['transaction_type']) && !empty($filters['transaction_type'])) {
            $this->db->where('t.transactiontype', $filters['transaction_type']);
        }

        if ($limit) {
            $this->db->limit($limit, $offset);
        }

        $this->db->order_by('h.changed_date', 'DESC');

        return $this->db->get();
    }

    /**
     * Get statistik perubahan status
     */
    public function getStatusChangeStatistics($filters = array())
    {
        // Check if table exists first
        if (!$this->db->table_exists($this->table)) {
            return array(); // Return empty array if table doesn't exist
        }

        $this->db->select('h.new_status, COUNT(*) as count')
            ->from($this->table . ' h')
            ->join('transactions t', 'h.transaction_id = t.id', 'left');

        // Apply same filters as getHistoryWithFilters
        if (isset($filters['user_role'])) {
            if ($filters['user_role'] === 'BUMDes') {
                $village_id = getVillageId();
                $current_user_id = getCurrentIdUser();

                if ($village_id) {
                    // Get all BUMDes users from the same village using separate query
                    $CI = &get_instance();
                    $village_bumdes_users = array();

                    // Use separate database query to avoid conflict
                    $bumdes_users_query = $CI->db->select('a.id')
                        ->from('msbumdesusers a')
                        ->join('msusers b', 'a.bumdes_id = b.id', 'inner')
                        ->where('b.villageid', $village_id)
                        ->get();

                    foreach ($bumdes_users_query->result() as $user) {
                        $village_bumdes_users[] = $user->id;
                    }

                    $allowed_creators = array_merge(array($current_user_id), $village_bumdes_users);
                    $this->db->where_in('t.createdby', $allowed_creators);
                } else {
                    $this->db->where('t.createdby', $current_user_id);
                }
            } elseif ($filters['user_role'] === 'BUMDes User') {
                $this->db->where('t.workunitid', getWorkunitId());
                $this->db->where('t.createdby', getBumdesId());
            }
        }

        if (isset($filters['date_from']) && !empty($filters['date_from'])) {
            $this->db->where('DATE(h.changed_date) >=', $filters['date_from']);
        }
        if (isset($filters['date_to']) && !empty($filters['date_to'])) {
            $this->db->where('DATE(h.changed_date) <=', $filters['date_to']);
        }

        $this->db->group_by('h.new_status');

        return $this->db->get()->result();
    }
}
