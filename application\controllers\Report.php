<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Transactions $transactions
 * @property Beginningbalances $beginningbalances
 * @property Workunits $workunits
 */
class Report extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Transactions', 'transactions');
        $this->load->model('Beginningbalances', 'beginningbalances');

        // Load PDF library
        require_once FCPATH . 'vendor/autoload.php';
        $this->load->model('Workunits', 'workunits');
    }

    public function balance()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $year = getGet('year', date('Y'));

        $data = array();
        $data['title'] = 'Laporan Saldo Awal';
        $data['content'] = 'report/balance';
        $data['year'] = $year;
        $data['transactions'] = $this->transactions->order_by('transactiondate', 'ASC')
            ->result(array(
                'YEAR(transactiondate) =' => $year,
                'createdby' => getCurrentIdUser(),
            ));
        $data['beginningbalance'] = $this->beginningbalances->sum('beginning_balances', array(
            'YEAR(period) =' => $year,
            'createdby' => getCurrentIdUser(),
        )) ?? 0;
        $data['beginningbalances'] = $this->beginningbalances->select('a.*, b.workunitcode, b.workunitname, COALESCE(c.total_income, 0) AS total_income, COALESCE(c.total_expense, 0) AS total_expense')
            ->join('msworkunits b', 'b.id = a.workunitid')
            ->join("(SELECT
                        createdby,
                        SUM(CASE WHEN transactiontype = 'Pendapatan' THEN amount ELSE 0 END) AS total_income,
                        SUM(CASE WHEN transactiontype = 'Pengeluaran' THEN amount ELSE 0 END) AS total_expense,
                        YEAR(transactiondate) AS transactiondate_year
                    FROM transactions
                    WHERE createdby = " . getCurrentIdUser() . "
                    GROUP BY createdby, YEAR(transactiondate)) c", 'c.createdby = a.createdby AND c.transactiondate_year = YEAR(a.period)', 'LEFT')
            ->order_by('a.period', 'ASC')
            ->result(array(
                'YEAR(a.period) =' => $year,
                'a.createdby' => getCurrentIdUser(),
            ));

        return $this->load->view('master', $data);
    }

    public function daily()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $date = getGet('date', date('Y-m-d'));

        $data = array();
        $data['title'] = 'Laporan Transaksi Harian';
        $data['content'] = 'report/daily';
        $data['date'] = $date;
        // Debug: Check if transactions table exists and has data
        if (!$this->db->table_exists('transactions')) {
            log_message('error', 'Table transactions does not exist');
            $data['transactions'] = array();
        } else {
            // Check if workunitid column exists
            $fields = $this->db->field_data('transactions');
            $has_workunitid = false;
            foreach ($fields as $field) {
                if ($field->name == 'workunitid') {
                    $has_workunitid = true;
                    break;
                }
            }

            // Filter berdasarkan role
            if (isBumdesUser()) {
                // Pengguna BUMDes hanya melihat data dari unit kerja mereka di desa yang sama
                if ($has_workunitid) {
                    $data['transactions'] = $this->transactions->select('a.*, COALESCE(CONCAT(w.workunitcode, " - ", w.workunitname), "N/A") as workunitdata')
                        ->join('msworkunits w', 'w.id = a.workunitid', 'LEFT')
                        ->order_by('a.transactiondate', 'ASC')
                        ->result(array(
                            'DATE(a.transactiondate)' => $date,
                            'a.workunitid' => getWorkunitId(),
                            'a.createdby' => getBumdesId(), // Transaksi dari BUMDes yang sama
                        ));
                } else {
                    // Fallback query without workunitid
                    $data['transactions'] = $this->transactions->select('a.*, "N/A" as workunitdata')
                        ->order_by('a.transactiondate', 'ASC')
                        ->result(array(
                            'DATE(a.transactiondate)' => $date,
                            'a.createdby' => getBumdesId(),
                        ));
                }

                // Debug log
                log_message('debug', 'Daily report (BUMDes User) - Date: ' . $date . ', BUMDes: ' . getBumdesId() . ', Workunit: ' . getWorkunitId() . ', Transactions count: ' . count($data['transactions']));
            } else {
                // BUMDes biasa - melihat data dari desa mereka sendiri (termasuk pengguna BUMDes)
                $village_id = getVillageId();
                $current_user_id = getCurrentIdUser();

                // Get all BUMDes users from the same village
                $village_bumdes_users = array();
                if ($village_id) {
                    $this->load->model('Bumdes_user_model', 'bumdes_users');
                    $bumdes_users_in_village = $this->bumdes_users->select('a.id')
                        ->join('msusers b', 'a.bumdes_id = b.id', 'inner')
                        ->result(array('b.villageid' => $village_id));

                    foreach ($bumdes_users_in_village as $user) {
                        $village_bumdes_users[] = $user->id;
                    }
                }

                // Include current BUMDes and all BUMDes users from same village
                $allowed_creators = array_merge(array($current_user_id), $village_bumdes_users);

                if ($has_workunitid) {
                    $data['transactions'] = $this->transactions->select('a.*, COALESCE(CONCAT(w.workunitcode, " - ", w.workunitname), "N/A") as workunitdata')
                        ->join('msworkunits w', 'w.id = a.workunitid', 'LEFT')
                        ->where('DATE(a.transactiondate)', $date)
                        ->where_in('a.createdby', $allowed_creators)
                        ->order_by('a.transactiondate', 'ASC')
                        ->get()
                        ->result();
                } else {
                    // Fallback query without workunitid
                    $data['transactions'] = $this->transactions->select('a.*, "N/A" as workunitdata')
                        ->where('DATE(a.transactiondate)', $date)
                        ->where_in('a.createdby', $allowed_creators)
                        ->order_by('a.transactiondate', 'ASC')
                        ->get()
                        ->result();
                }

                // Debug log
                log_message('debug', 'Daily report (BUMDes) - Date: ' . $date . ', User: ' . $current_user_id . ', Village: ' . $village_id . ', Allowed creators: ' . implode(',', $allowed_creators) . ', Transactions count: ' . count($data['transactions']));
            }
        }
        // Calculate totals based on user role
        if (isBumdesUser()) {
            // Pengguna BUMDes: filter by workunit and BUMDes
            $data['total_income'] = $this->transactions->sum('amount', array(
                'DATE(transactiondate)' => $date,
                'transactiontype' => 'Pendapatan',
                'workunitid' => getWorkunitId(),
                'createdby' => getBumdesId(),
            )) ?? 0;
            $data['total_expense'] = $this->transactions->sum('amount', array(
                'DATE(transactiondate)' => $date,
                'transactiontype' => 'Pengeluaran',
                'workunitid' => getWorkunitId(),
                'createdby' => getBumdesId(),
            )) ?? 0;
        } else {
            // BUMDes biasa: filter by village (include BUMDes users from same village)
            $village_id = getVillageId();
            $current_user_id = getCurrentIdUser();

            // Get all BUMDes users from the same village
            $village_bumdes_users = array();
            if ($village_id) {
                $this->load->model('Bumdes_user_model', 'bumdes_users');
                $bumdes_users_in_village = $this->bumdes_users->select('a.id')
                    ->join('msusers b', 'a.bumdes_id = b.id', 'inner')
                    ->result(array('b.villageid' => $village_id));

                foreach ($bumdes_users_in_village as $user) {
                    $village_bumdes_users[] = $user->id;
                }
            }

            // Include current BUMDes and all BUMDes users from same village
            $allowed_creators = array_merge(array($current_user_id), $village_bumdes_users);

            // Calculate totals using custom query for multiple creators
            $income_query = $this->db->select('SUM(amount) as total')
                ->from('transactions')
                ->where('DATE(transactiondate)', $date)
                ->where('transactiontype', 'Pendapatan')
                ->where_in('createdby', $allowed_creators)
                ->get();
            $data['total_income'] = $income_query->row()->total ?? 0;

            $expense_query = $this->db->select('SUM(amount) as total')
                ->from('transactions')
                ->where('DATE(transactiondate)', $date)
                ->where('transactiontype', 'Pengeluaran')
                ->where_in('createdby', $allowed_creators)
                ->get();
            $data['total_expense'] = $expense_query->row()->total ?? 0;
        }

        return $this->load->view('master', $data);
    }

    public function monthly()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $month = getGet('month', date('Y-m'));

        $data = array();
        $data['title'] = 'Laporan Transaksi Bulanan';
        $data['content'] = 'report/monthly';
        $data['month'] = $month;
        // Calculate totals based on user role
        if (isBumdesUser()) {
            // Pengguna BUMDes: filter by workunit and BUMDes
            $data['total_income'] = $this->transactions->sum('amount', array(
                'YEAR(transactiondate)' => date('Y', strtotime($month)),
                'MONTH(transactiondate)' => date('m', strtotime($month)),
                'transactiontype' => 'Pendapatan',
                'workunitid' => getWorkunitId(),
                'createdby' => getBumdesId(),
            )) ?? 0;
            $data['total_expense'] = $this->transactions->sum('amount', array(
                'YEAR(transactiondate)' => date('Y', strtotime($month)),
                'MONTH(transactiondate)' => date('m', strtotime($month)),
                'transactiontype' => 'Pengeluaran',
                'workunitid' => getWorkunitId(),
                'createdby' => getBumdesId(),
            )) ?? 0;
        } else {
            // BUMDes biasa: filter by village (include BUMDes users from same village)
            $village_id = getVillageId();
            $current_user_id = getCurrentIdUser();

            // Get all BUMDes users from the same village
            $village_bumdes_users = array();
            if ($village_id) {
                $this->load->model('Bumdes_user_model', 'bumdes_users');
                $bumdes_users_in_village = $this->bumdes_users->select('a.id')
                    ->join('msusers b', 'a.bumdes_id = b.id', 'inner')
                    ->result(array('b.villageid' => $village_id));

                foreach ($bumdes_users_in_village as $user) {
                    $village_bumdes_users[] = $user->id;
                }
            }

            // Include current BUMDes and all BUMDes users from same village
            $allowed_creators = array_merge(array($current_user_id), $village_bumdes_users);

            // Calculate totals using custom query for multiple creators
            $income_query = $this->db->select('SUM(amount) as total')
                ->from('transactions')
                ->where('YEAR(transactiondate)', date('Y', strtotime($month)))
                ->where('MONTH(transactiondate)', date('m', strtotime($month)))
                ->where('transactiontype', 'Pendapatan')
                ->where_in('createdby', $allowed_creators)
                ->get();
            $data['total_income'] = $income_query->row()->total ?? 0;

            $expense_query = $this->db->select('SUM(amount) as total')
                ->from('transactions')
                ->where('YEAR(transactiondate)', date('Y', strtotime($month)))
                ->where('MONTH(transactiondate)', date('m', strtotime($month)))
                ->where('transactiontype', 'Pengeluaran')
                ->where_in('createdby', $allowed_creators)
                ->get();
            $data['total_expense'] = $expense_query->row()->total ?? 0;
        }
        $data['transactions'] = $this->transactions;

        // Debug: Check if transactions table exists and has data
        if (!$this->db->table_exists('transactions')) {
            log_message('error', 'Table transactions does not exist');
            $data['monthly_transactions'] = array();
        } else {
            // Check if workunitid column exists
            $fields = $this->db->field_data('transactions');
            $has_workunitid = false;
            foreach ($fields as $field) {
                if ($field->name == 'workunitid') {
                    $has_workunitid = true;
                    break;
                }
            }

            // Filter berdasarkan role
            if (isBumdesUser()) {
                // Pengguna BUMDes hanya melihat data dari unit kerja mereka di desa yang sama
                if ($has_workunitid) {
                    $data['monthly_transactions'] = $this->transactions->select('a.*, COALESCE(CONCAT(w.workunitcode, " - ", w.workunitname), "N/A") as workunitdata')
                        ->join('msworkunits w', 'w.id = a.workunitid', 'LEFT')
                        ->order_by('a.transactiondate', 'ASC')
                        ->result(array(
                            'YEAR(a.transactiondate)' => date('Y', strtotime($month)),
                            'MONTH(a.transactiondate)' => date('m', strtotime($month)),
                            'a.workunitid' => getWorkunitId(),
                            'a.createdby' => getBumdesId(),
                        ));
                } else {
                    // Fallback query without workunitid
                    $data['monthly_transactions'] = $this->transactions->select('a.*, "N/A" as workunitdata')
                        ->order_by('a.transactiondate', 'ASC')
                        ->result(array(
                            'YEAR(a.transactiondate)' => date('Y', strtotime($month)),
                            'MONTH(a.transactiondate)' => date('m', strtotime($month)),
                            'a.createdby' => getBumdesId(),
                        ));
                }

                // Debug log
                log_message('debug', 'Monthly report (BUMDes User) - Month: ' . $month . ', BUMDes: ' . getBumdesId() . ', Workunit: ' . getWorkunitId() . ', Transactions count: ' . count($data['monthly_transactions']));
            } else {
                // BUMDes biasa - melihat data dari desa mereka sendiri (termasuk pengguna BUMDes)
                $village_id = getVillageId();
                $current_user_id = getCurrentIdUser();

                // Get all BUMDes users from the same village
                $village_bumdes_users = array();
                if ($village_id) {
                    $this->load->model('Bumdes_user_model', 'bumdes_users');
                    $bumdes_users_in_village = $this->bumdes_users->select('a.id')
                        ->join('msusers b', 'a.bumdes_id = b.id', 'inner')
                        ->result(array('b.villageid' => $village_id));

                    foreach ($bumdes_users_in_village as $user) {
                        $village_bumdes_users[] = $user->id;
                    }
                }

                // Include current BUMDes and all BUMDes users from same village
                $allowed_creators = array_merge(array($current_user_id), $village_bumdes_users);

                if ($has_workunitid) {
                    $data['monthly_transactions'] = $this->transactions->select('a.*, COALESCE(CONCAT(w.workunitcode, " - ", w.workunitname), "N/A") as workunitdata')
                        ->join('msworkunits w', 'w.id = a.workunitid', 'LEFT')
                        ->where('YEAR(a.transactiondate)', date('Y', strtotime($month)))
                        ->where('MONTH(a.transactiondate)', date('m', strtotime($month)))
                        ->where_in('a.createdby', $allowed_creators)
                        ->order_by('a.transactiondate', 'ASC')
                        ->get()
                        ->result();
                } else {
                    // Fallback query without workunitid
                    $data['monthly_transactions'] = $this->transactions->select('a.*, "N/A" as workunitdata')
                        ->where('YEAR(a.transactiondate)', date('Y', strtotime($month)))
                        ->where('MONTH(a.transactiondate)', date('m', strtotime($month)))
                        ->where_in('a.createdby', $allowed_creators)
                        ->order_by('a.transactiondate', 'ASC')
                        ->get()
                        ->result();
                }

                // Debug log
                log_message('debug', 'Monthly report (BUMDes) - Month: ' . $month . ', User: ' . $current_user_id . ', Village: ' . $village_id . ', Allowed creators: ' . implode(',', $allowed_creators) . ', Transactions count: ' . count($data['monthly_transactions']));
            }
        }

        return $this->load->view('master', $data);
    }

    public function daily_pdf($date = null)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        if (!$date) {
            $date = date('Y-m-d');
        }

        // Get the same data as daily report
        $data = $this->getDailyReportData($date);
        $data['date'] = $date;
        $data['village_name'] = getVillageName() ?: 'N/A';

        // Generate PDF
        $html = $this->load->view('report/daily_pdf', $data, true);

        $dompdf = new \Dompdf\Dompdf();
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();

        $filename = 'Laporan_Harian_BUMDes_' . date('d-m-Y', strtotime($date)) . '.pdf';
        $dompdf->stream($filename, array('Attachment' => true));
    }

    public function monthly_pdf($month = null)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        if (!$month) {
            $month = date('Y-m');
        }

        // Get the same data as monthly report
        $data = $this->getMonthlyReportData($month);
        $data['month'] = $month;
        $data['village_name'] = getVillageName() ?: 'N/A';

        // Generate PDF
        $html = $this->load->view('report/monthly_pdf', $data, true);

        $dompdf = new \Dompdf\Dompdf();
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();

        $filename = 'Laporan_Bulanan_BUMDes_Desa_' . $data['village_name'] . '_' . date('m-Y', strtotime($month)) . '.pdf';
        $dompdf->stream($filename, array('Attachment' => true));
    }

    private function getDailyReportData($date)
    {
        $data = array();

        // Calculate totals based on user role
        if (isBumdesUser()) {
            // Pengguna BUMDes: filter by workunit and BUMDes
            $data['total_income'] = $this->transactions->sum('amount', array(
                'DATE(transactiondate)' => $date,
                'transactiontype' => 'Pendapatan',
                'workunitid' => getWorkunitId(),
                'createdby' => getBumdesId(),
            )) ?? 0;
            $data['total_expense'] = $this->transactions->sum('amount', array(
                'DATE(transactiondate)' => $date,
                'transactiontype' => 'Pengeluaran',
                'workunitid' => getWorkunitId(),
                'createdby' => getBumdesId(),
            )) ?? 0;

            // Get transactions with workunit data
            if ($this->db->field_exists('workunitid', 'transactions')) {
                $data['transactions'] = $this->transactions->select('a.*, COALESCE(b.workunitname, "N/A") as workunitdata')
                    ->join('msworkunits b', 'a.workunitid = b.id', 'left')
                    ->order_by('a.transactiondate', 'ASC')
                    ->result(array(
                        'DATE(a.transactiondate)' => $date,
                        'a.workunitid' => getWorkunitId(),
                        'a.createdby' => getBumdesId(),
                    ));
            } else {
                $data['transactions'] = $this->transactions->select('a.*, "N/A" as workunitdata')
                    ->order_by('a.transactiondate', 'ASC')
                    ->result(array(
                        'DATE(a.transactiondate)' => $date,
                        'a.createdby' => getBumdesId(),
                    ));
            }
        } else {
            // BUMDes biasa - melihat data dari desa mereka sendiri
            $village_id = getVillageId();
            $current_user_id = getCurrentIdUser();

            // Get all BUMDes users from the same village
            $village_bumdes_users = array();
            if ($village_id) {
                $this->load->model('Bumdes_user_model', 'bumdes_users');
                $bumdes_users_in_village = $this->bumdes_users->select('a.id')
                    ->join('msusers b', 'a.bumdes_id = b.id', 'inner')
                    ->result(array('b.villageid' => $village_id));

                foreach ($bumdes_users_in_village as $user) {
                    $village_bumdes_users[] = $user->id;
                }
            }

            $allowed_creators = array_merge(array($current_user_id), $village_bumdes_users);

            if (!empty($allowed_creators)) {
                // Calculate totals using custom query
                $income_query = $this->db->select('SUM(amount) as total')
                    ->from('transactions')
                    ->where('DATE(transactiondate)', $date)
                    ->where('transactiontype', 'Pendapatan')
                    ->where_in('createdby', $allowed_creators)
                    ->get();
                $data['total_income'] = $income_query->row()->total ?? 0;

                $expense_query = $this->db->select('SUM(amount) as total')
                    ->from('transactions')
                    ->where('DATE(transactiondate)', $date)
                    ->where('transactiontype', 'Pengeluaran')
                    ->where_in('createdby', $allowed_creators)
                    ->get();
                $data['total_expense'] = $expense_query->row()->total ?? 0;

                // Get transactions
                $data['transactions'] = $this->transactions->select('a.*, COALESCE(b.workunitname, "N/A") as workunitdata')
                    ->join('msworkunits b', 'a.workunitid = b.id', 'left')
                    ->order_by('a.transactiondate', 'ASC')
                    ->where('DATE(a.transactiondate)', $date)
                    ->where_in('a.createdby', $allowed_creators)
                    ->result();
            } else {
                $data['total_income'] = 0;
                $data['total_expense'] = 0;
                $data['transactions'] = array();
            }
        }

        $data['total_profit'] = $data['total_income'] - $data['total_expense'];

        return $data;
    }

    private function getMonthlyReportData($month)
    {
        $data = array();

        // Calculate totals based on user role
        if (isBumdesUser()) {
            // Pengguna BUMDes: filter by workunit and BUMDes
            $data['total_income'] = $this->transactions->sum('amount', array(
                'YEAR(transactiondate)' => date('Y', strtotime($month)),
                'MONTH(transactiondate)' => date('m', strtotime($month)),
                'transactiontype' => 'Pendapatan',
                'workunitid' => getWorkunitId(),
                'createdby' => getBumdesId(),
            )) ?? 0;
            $data['total_expense'] = $this->transactions->sum('amount', array(
                'YEAR(transactiondate)' => date('Y', strtotime($month)),
                'MONTH(transactiondate)' => date('m', strtotime($month)),
                'transactiontype' => 'Pengeluaran',
                'workunitid' => getWorkunitId(),
                'createdby' => getBumdesId(),
            )) ?? 0;

            // Get transactions with workunit data
            if ($this->db->field_exists('workunitid', 'transactions')) {
                $data['monthly_transactions'] = $this->transactions->select('a.*, COALESCE(b.workunitname, "N/A") as workunitdata')
                    ->join('msworkunits b', 'a.workunitid = b.id', 'left')
                    ->order_by('a.transactiondate', 'ASC')
                    ->result(array(
                        'YEAR(a.transactiondate)' => date('Y', strtotime($month)),
                        'MONTH(a.transactiondate)' => date('m', strtotime($month)),
                        'a.workunitid' => getWorkunitId(),
                        'a.createdby' => getBumdesId(),
                    ));
            } else {
                $data['monthly_transactions'] = $this->transactions->select('a.*, "N/A" as workunitdata')
                    ->order_by('a.transactiondate', 'ASC')
                    ->result(array(
                        'YEAR(a.transactiondate)' => date('Y', strtotime($month)),
                        'MONTH(a.transactiondate)' => date('m', strtotime($month)),
                        'a.createdby' => getBumdesId(),
                    ));
            }
        } else {
            // BUMDes biasa - melihat data dari desa mereka sendiri
            $village_id = getVillageId();
            $current_user_id = getCurrentIdUser();

            // Get all BUMDes users from the same village
            $village_bumdes_users = array();
            if ($village_id) {
                $this->load->model('Bumdes_user_model', 'bumdes_users');
                $bumdes_users_in_village = $this->bumdes_users->select('a.id')
                    ->join('msusers b', 'a.bumdes_id = b.id', 'inner')
                    ->result(array('b.villageid' => $village_id));

                foreach ($bumdes_users_in_village as $user) {
                    $village_bumdes_users[] = $user->id;
                }
            }

            $allowed_creators = array_merge(array($current_user_id), $village_bumdes_users);

            if (!empty($allowed_creators)) {
                // Calculate totals using custom query
                $income_query = $this->db->select('SUM(amount) as total')
                    ->from('transactions')
                    ->where('YEAR(transactiondate)', date('Y', strtotime($month)))
                    ->where('MONTH(transactiondate)', date('m', strtotime($month)))
                    ->where('transactiontype', 'Pendapatan')
                    ->where_in('createdby', $allowed_creators)
                    ->get();
                $data['total_income'] = $income_query->row()->total ?? 0;

                $expense_query = $this->db->select('SUM(amount) as total')
                    ->from('transactions')
                    ->where('YEAR(transactiondate)', date('Y', strtotime($month)))
                    ->where('MONTH(transactiondate)', date('m', strtotime($month)))
                    ->where('transactiontype', 'Pengeluaran')
                    ->where_in('createdby', $allowed_creators)
                    ->get();
                $data['total_expense'] = $expense_query->row()->total ?? 0;

                // Get transactions
                $data['monthly_transactions'] = $this->transactions->select('a.*, COALESCE(b.workunitname, "N/A") as workunitdata')
                    ->join('msworkunits b', 'a.workunitid = b.id', 'left')
                    ->order_by('a.transactiondate', 'ASC')
                    ->where('YEAR(a.transactiondate)', date('Y', strtotime($month)))
                    ->where('MONTH(a.transactiondate)', date('m', strtotime($month)))
                    ->where_in('a.createdby', $allowed_creators)
                    ->result();
            } else {
                $data['total_income'] = 0;
                $data['total_expense'] = 0;
                $data['monthly_transactions'] = array();
            }
        }

        $data['total_profit'] = $data['total_income'] - $data['total_expense'];

        return $data;
    }
}
