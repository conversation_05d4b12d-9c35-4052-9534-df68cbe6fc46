<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Edit Pengguna</h6>

    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">
            <a href="<?= base_url('bumdes_users') ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                Manajemen Pengguna
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">Edit</li>
    </ul>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Edit Pengguna: <?= $user->name ?></h5>
    </div>

    <div class="card-body">
        <form id="frm" action="<?= base_url('bumdes_users/process_edit') ?>" method="POST" autocomplete="off" success-redirect="<?= base_url('bumdes_users') ?>">
            <input type="hidden" name="id" value="<?= $user->id ?>">

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control" placeholder="Masukkan nama lengkap" value="<?= $user->name ?>" required>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label">Nomor WhatsApp</label>
                        <input type="text" name="phone" class="form-control" placeholder="Contoh: 08123456789" value="<?= $user->phone ?>">
                        <small class="text-muted">Format: 08xxxxxxxxxx atau +62xxxxxxxxxx</small>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="" class="form-label">Alamat</label>
                        <textarea name="address" class="form-control" rows="3" placeholder="Masukkan alamat lengkap"><?= $user->address ?></textarea>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label">Username <span class="text-danger">*</span></label>
                        <input type="text" name="username" class="form-control" placeholder="Masukkan username" value="<?= $user->username ?>" required>
                        <small class="text-muted">Minimal 4 karakter, hanya huruf, angka, dan underscore</small>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="" class="form-label">Password</label>
                        <div class="position-relative">
                            <input type="password" name="password" id="password" class="form-control" placeholder="Kosongkan jika tidak ingin mengubah password">
                            <span class="position-absolute top-50 translate-middle-y end-0 me-12 cursor-pointer toggle-password" data-target="#password">
                                <iconify-icon icon="solar:eye-outline" class="icon text-xl line-height-1"></iconify-icon>
                            </span>
                        </div>
                        <small class="text-muted">Kosongkan jika tidak ingin mengubah password. Minimal 6 karakter jika diisi.</small>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="" class="form-label">Unit Usaha <span class="text-danger">*</span></label>
                        <select name="workunitid" class="form-control" required>
                            <option value="">Pilih Unit Usaha</option>
                            <?php foreach ($workunits as $workunit): ?>
                                <option value="<?= $workunit->id ?>" <?= $workunit->id == $user->workunitid ? 'selected' : '' ?>>
                                    <?= $workunit->workunitname ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <small class="text-muted">Pengguna hanya dapat mengelola transaksi untuk unit usaha yang dipilih</small>
                    </div>
                </div>
            </div>

            <div class="text-end mt-3">
                <a href="<?= base_url('bumdes_users') ?>" class="btn btn-danger btn-sm">Kembali</a>
                <button type="submit" class="btn btn-primary btn-sm">Simpan Perubahan</button>
            </div>
        </form>
    </div>
</div>

<script>
    window.onload = function() {
        // Password toggle functionality
        function initializePasswordToggle(selector) {
            $(selector).on('click', function() {
                const target = $(this).data('target');
                const input = $(target);
                const icon = $(this).find('iconify-icon');

                if (input.attr('type') === 'password') {
                    input.attr('type', 'text');
                    icon.attr('icon', 'solar:eye-closed-outline');
                } else {
                    input.attr('type', 'password');
                    icon.attr('icon', 'solar:eye-outline');
                }
            });
        }

        // Initialize password toggle
        initializePasswordToggle('.toggle-password');

        // Form validation
        $('#frm').on('submit', function(e) {
            const username = $('input[name="username"]').val();
            const password = $('input[name="password"]').val();

            // Validate username format
            const usernameRegex = /^[a-zA-Z0-9_]+$/;
            if (!usernameRegex.test(username)) {
                e.preventDefault();
                Swal.fire({
                    title: 'Format Username Salah',
                    text: 'Username hanya boleh mengandung huruf, angka, dan underscore.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return false;
            }

            // Validate password length if filled
            if (password && password.length < 6) {
                e.preventDefault();
                Swal.fire({
                    title: 'Password Terlalu Pendek',
                    text: 'Password minimal 6 karakter.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return false;
            }
        });

        // Phone number formatting
        $('input[name="phone"]').on('input', function() {
            let value = $(this).val().replace(/[^0-9+]/g, '');

            // Auto format Indonesian phone number
            if (value.startsWith('0')) {
                value = '+62' + value.substring(1);
            } else if (value.startsWith('62') && !value.startsWith('+62')) {
                value = '+' + value;
            }

            $(this).val(value);
        });

        // AJAX form submission
        $.AjaxRequest('#frm', {
            success: function(response) {
                if (response.RESULT === 'OK') {
                    Swal.fire({
                        title: 'Berhasil!',
                        text: response.MESSAGE,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        window.location.href = '<?= base_url('bumdes_users') ?>';
                    });
                } else {
                    Swal.fire({
                        title: 'Gagal!',
                        text: response.MESSAGE,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },
            error: function() {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan sistem.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    };
</script>