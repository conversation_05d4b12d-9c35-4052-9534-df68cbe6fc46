<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">History Perubahan Status Transaksi</h6>
    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">History Status Transaksi</li>
    </ul>
</div>

<!-- Filter Section -->
<div class="card mb-24">
    <div class="card-header">
        <h6 class="card-title mb-0">Filter Data</h6>
    </div>
    <div class="card-body">
        <form method="POST" action="<?= base_url('transaction_history') ?>">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Tanggal Dari</label>
                        <input type="date" name="date_from" class="form-control" value="<?= $date_from ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Tanggal Sampai</label>
                        <input type="date" name="date_to" class="form-control" value="<?= $date_to ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-control">
                            <option value="">Semua Status</option>
                            <option value="Lunas" <?= $status === 'Lunas' ? 'selected' : '' ?>>Lunas</option>
                            <option value="Menunggu Pembayaran" <?= $status === 'Menunggu Pembayaran' ? 'selected' : '' ?>>Menunggu Pembayaran</option>
                            <option value="Dibatalkan" <?= $status === 'Dibatalkan' ? 'selected' : '' ?>>Dibatalkan</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">Jenis Transaksi</label>
                        <select name="transaction_type" class="form-control">
                            <option value="">Semua Jenis</option>
                            <option value="Pendapatan" <?= $transaction_type === 'Pendapatan' ? 'selected' : '' ?>>Pendapatan</option>
                            <option value="Pengeluaran" <?= $transaction_type === 'Pengeluaran' ? 'selected' : '' ?>>Pengeluaran</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <iconify-icon icon="solar:filter-bold" class="icon text-lg"></iconify-icon>
                    Filter
                </button>
                <button type="button" class="btn btn-success" onclick="exportData()">
                    <iconify-icon icon="solar:export-bold" class="icon text-lg"></iconify-icon>
                    Export Excel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Statistics Cards -->
<?php if (!empty($statistics)): ?>
<div class="row gy-4 mb-24">
    <?php foreach ($statistics as $stat): ?>
    <div class="col-md-4">
        <div class="card shadow-none border h-100">
            <div class="card-body p-20">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                    <div>
                        <p class="fw-medium text-primary-light mb-1">Status: <?= $stat->new_status ?></p>
                        <h6 class="mb-0"><?= number_format($stat->count) ?> Perubahan</h6>
                    </div>
                    <div class="w-50-px h-50-px bg-primary rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:chart-2-bold" class="text-white text-2xl mb-0"></iconify-icon>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>
<?php endif; ?>

<!-- History Table -->
<div class="card">
    <div class="card-header">
        <h6 class="card-title mb-0">Data History Perubahan Status</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table bordered-table mb-0 datatables">
                <thead>
                    <tr>
                        <th>Kode Transaksi</th>
                        <th>Tanggal Transaksi</th>
                        <th>Nominal</th>
                        <th>Status Lama</th>
                        <th>Status Baru</th>
                        <th>Diubah Oleh</th>
                        <th>Tanggal Perubahan</th>
                        <?php if (isSuperAdmin() || isBumdes()): ?>
                        <th>Unit Usaha</th>
                        <?php endif; ?>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($history as $item): ?>
                    <tr>
                        <td>
                            <span class="fw-medium text-primary-600"><?= $item->transactioncode ?></span>
                        </td>
                        <td><?= tgl_indo($item->transactiondate) ?></td>
                        <td>Rp <?= number_format($item->amount, 0, ',', '.') ?></td>
                        <td>
                            <?php if ($item->old_status): ?>
                                <span class="bg-secondary-focus text-secondary-main px-16 py-4 rounded-pill fw-medium text-sm">
                                    <?= $item->old_status ?>
                                </span>
                            <?php else: ?>
                                <span class="text-secondary-light">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($item->new_status == 'Lunas'): ?>
                                <span class="bg-success-focus text-success-main px-16 py-4 rounded-pill fw-medium text-sm">Lunas</span>
                            <?php elseif ($item->new_status == 'Menunggu Pembayaran'): ?>
                                <span class="bg-warning-focus text-warning-main px-16 py-4 rounded-pill fw-medium text-sm">Menunggu Pembayaran</span>
                            <?php elseif ($item->new_status == 'Dibatalkan'): ?>
                                <span class="bg-danger-focus text-danger-main px-16 py-4 rounded-pill fw-medium text-sm">Dibatalkan</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div>
                                <span class="fw-medium"><?= $item->changed_by_name ?></span>
                                <br>
                                <small class="text-secondary-light"><?= $item->changed_by_role ?></small>
                            </div>
                        </td>
                        <td><?= date('d/m/Y H:i', strtotime($item->changed_date)) ?></td>
                        <?php if (isSuperAdmin() || isBumdes()): ?>
                        <td><?= $item->workunitname ?: '-' ?></td>
                        <?php endif; ?>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewTransactionHistory(<?= $item->transaction_id ?>)">
                                <iconify-icon icon="solar:eye-bold" class="icon"></iconify-icon>
                                Detail
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal for Transaction History Detail -->
<div class="modal fade" id="historyDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detail History Transaksi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="historyDetailContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('.datatables').DataTable({
        "pageLength": 25,
        "order": [[6, 'desc']], // Sort by change date
        "language": {
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Tidak ada data yang ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data tersedia",
            "infoFiltered": "(difilter dari _MAX_ total data)",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });
});

function viewTransactionHistory(transactionId) {
    $.ajax({
        url: '<?= base_url('transaction_history/get_transaction_history') ?>',
        type: 'POST',
        data: { transaction_id: transactionId },
        dataType: 'json',
        success: function(response) {
            if (response.RESULT === 'OK') {
                let content = '<div class="table-responsive"><table class="table table-bordered">';
                content += '<thead><tr><th>Status Lama</th><th>Status Baru</th><th>Diubah Oleh</th><th>Tanggal</th><th>Catatan</th></tr></thead><tbody>';
                
                if (response.DATA && response.DATA.length > 0) {
                    response.DATA.forEach(function(item) {
                        content += '<tr>';
                        content += '<td>' + (item.old_status || '-') + '</td>';
                        content += '<td>' + item.new_status + '</td>';
                        content += '<td>' + item.changed_by_name + ' (' + item.changed_by_role + ')</td>';
                        content += '<td>' + new Date(item.changed_date).toLocaleString('id-ID') + '</td>';
                        content += '<td>' + (item.notes || '-') + '</td>';
                        content += '</tr>';
                    });
                } else {
                    content += '<tr><td colspan="5" class="text-center">Tidak ada history perubahan status</td></tr>';
                }
                
                content += '</tbody></table></div>';
                
                $('#historyDetailContent').html(content);
                $('#historyDetailModal').modal('show');
            } else {
                Swal.fire('Error', response.MESSAGE, 'error');
            }
        },
        error: function() {
            Swal.fire('Error', 'Terjadi kesalahan saat mengambil data', 'error');
        }
    });
}

function exportData() {
    // Create form for export
    let form = $('<form>', {
        'method': 'POST',
        'action': '<?= base_url('transaction_history/export') ?>'
    });
    
    // Add current filter values
    form.append($('<input>', { 'type': 'hidden', 'name': 'date_from', 'value': $('input[name="date_from"]').val() }));
    form.append($('<input>', { 'type': 'hidden', 'name': 'date_to', 'value': $('input[name="date_to"]').val() }));
    form.append($('<input>', { 'type': 'hidden', 'name': 'status', 'value': $('select[name="status"]').val() }));
    form.append($('<input>', { 'type': 'hidden', 'name': 'transaction_type', 'value': $('select[name="transaction_type"]').val() }));
    
    $('body').append(form);
    form.submit();
    form.remove();
}
</script>
