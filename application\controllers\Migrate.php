<?php
defined('BASEPATH') or die('No direct script access allowed!');

class <PERSON>grate extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->library('migration');
        
        // Only allow access from localhost or specific IPs for security
        if (!in_array($_SERVER['REMOTE_ADDR'], array('127.0.0.1', '::1'))) {
            show_404();
        }
    }

    public function index()
    {
        if ($this->migration->current() === FALSE) {
            show_error($this->migration->error_string());
        } else {
            echo "Migration completed successfully!";
        }
    }

    public function version($version = null)
    {
        if ($version === null) {
            echo "Current migration version: " . $this->migration->current();
        } else {
            if ($this->migration->version($version) === FALSE) {
                show_error($this->migration->error_string());
            } else {
                echo "Migration to version $version completed successfully!";
            }
        }
    }
}
