<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Migration_Add_status_to_msusers extends CI_Migration
{
    public function up()
    {
        // Check if status column doesn't exist
        if (!$this->db->field_exists('status', 'msusers')) {
            // Add status column to msusers table
            $fields = array(
                'status' => array(
                    'type' => 'ENUM',
                    'constraint' => array('Pending', 'Aktif', 'Nonaktif'),
                    'default' => 'Aktif',
                    'after' => 'role'
                )
            );
            
            $this->dbforge->add_column('msusers', $fields);
            
            // Update existing BUMDes users created by self-registration (createdby = 0) to Pending status
            $this->db->where('role', 'BUMDes');
            $this->db->where('createdby', 0);
            $this->db->update('msusers', array('status' => 'Pending'));
        }
    }

    public function down()
    {
        // Remove status column
        if ($this->db->field_exists('status', 'msusers')) {
            $this->dbforge->drop_column('msusers', 'status');
        }
    }
}
