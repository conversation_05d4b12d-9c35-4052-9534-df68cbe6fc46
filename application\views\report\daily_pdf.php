<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }

        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }

        .header h2 {
            margin: 5px 0;
            font-size: 16px;
            color: #666;
        }

        .info-section {
            margin-bottom: 20px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .summary-table th,
        .summary-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .summary-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .transactions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .transactions-table th,
        .transactions-table td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            font-size: 10px;
        }

        .transactions-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .income {
            color: #28a745;
        }

        .expense {
            color: #dc3545;
        }

        .profit {
            color: #007bff;
            font-weight: bold;
        }

        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 10px;
            color: #666;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>LAPORAN HARIAN BUMDES</h1>
        <h2>Desa <?= $village_name ?></h2>
        <p>Tanggal: <?= tgl_indo($date) ?></p>
    </div>

    <div class="info-section">
        <div class="info-row">
            <span><strong>Periode:</strong> <?= tgl_indo($date) ?></span>
            <span><strong>Dicetak:</strong> <?= tgl_indo(date('Y-m-d')) ?> <?= date('H:i') ?> WIB</span>
        </div>
        <?php if (isBumdesUser()): ?>
            <div class="info-row">
                <span><strong>Unit Usaha:</strong> <?= getWorkunitName() ?></span>
                <span><strong>BUMDes:</strong> <?= getVillageName() ?? 'N/A' ?></span>
            </div>
        <?php endif; ?>
    </div>

    <table class="summary-table">
        <thead>
            <tr>
                <th>Keterangan</th>
                <th class="text-right">Jumlah (Rp)</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Total Pendapatan</td>
                <td class="text-right income"><?= number_format($total_income, 0, ',', '.') ?></td>
            </tr>
            <tr>
                <td>Total Pengeluaran</td>
                <td class="text-right expense"><?= number_format($total_expense, 0, ',', '.') ?></td>
            </tr>
            <tr style="border-top: 2px solid #333;">
                <td><strong>Keuntungan Bersih</strong></td>
                <td class="text-right profit"><?= number_format($total_profit, 0, ',', '.') ?></td>
            </tr>
        </tbody>
    </table>

    <?php if (!empty($transactions)): ?>
        <h3>Detail Transaksi</h3>
        <table class="transactions-table">
            <thead>
                <tr>
                    <th>No</th>
                    <th>Kode</th>
                    <th>Tanggal</th>
                    <th>Keterangan</th>
                    <th>Unit Usaha</th>
                    <th>Jenis</th>
                    <th class="text-right">Jumlah (Rp)</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <?php $no = 1;
                foreach ($transactions as $transaction): ?>
                    <tr>
                        <td class="text-center"><?= $no++ ?></td>
                        <td><?= $transaction->transactioncode ?></td>
                        <td><?= date('d/m/Y', strtotime($transaction->transactiondate)) ?></td>
                        <td><?= $transaction->transactionnote ?></td>
                        <td><?= $transaction->workunitdata ?></td>
                        <td class="<?= $transaction->transactiontype == 'Pendapatan' ? 'income' : 'expense' ?>">
                            <?= $transaction->transactiontype ?>
                        </td>
                        <td class="text-right"><?= number_format($transaction->amount, 0, ',', '.') ?></td>
                        <td><?= $transaction->status ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <p class="text-center" style="margin-top: 20px; color: #666;">Tidak ada transaksi pada tanggal ini.</p>
    <?php endif; ?>

    <div class="footer">
        <p>Laporan ini digenerate secara otomatis oleh Sistem BUMDes</p>
        <p>Dicetak pada: <?= date('d/m/Y H:i:s') ?> WIB</p>
    </div>
</body>

</html>