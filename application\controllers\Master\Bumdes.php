<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Workunits $workunits
 * @property MsProvinces $msprovinces
 * @property Superadmins $superadmins
 * @property CI_Form_validation $form_validation
 */
class Bumdes extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Workunits', 'workunits');
        $this->load->model('MsProvinces', 'msprovinces');
        $this->load->model('Superadmins', 'superadmins');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'BUMDes';
        $data['content'] = 'master/bumdes/index';

        // Get BUMDes data without workunit join first
        $bumdes_raw = $this->superadmins->select('a.*, c.name as village, d.name as district, e.name as city, f.name as province')
            ->join('msvillages c', 'a.villageid = CAST(c.code AS UNSIGNED)')
            ->join('msdistricts d', 'c.district_code = d.code')
            ->join('mscities e', 'd.city_code = e.code')
            ->join('msprovinces f', 'e.province_code = f.code')
            ->order_by('a.status', 'ASC') // Show pending first
            ->order_by('a.createddate', 'DESC')
            ->result(array(
                'a.role' => 'BUMDes',
            ));

        // Process each BUMDes to get workunit names
        $bumdes_processed = array();
        foreach ($bumdes_raw as $bumdes) {
            $workunit_names = array();
            if (!empty($bumdes->workunitid)) {
                $workunit_ids = explode(',', $bumdes->workunitid);
                foreach ($workunit_ids as $workunit_id) {
                    $workunit = $this->workunits->get(array('id' => trim($workunit_id)))->row();
                    if ($workunit) {
                        $workunit_names[] = $workunit->workunitname;
                    }
                }
            }
            $bumdes->workunit_display = implode(', ', $workunit_names);
            $bumdes_processed[] = $bumdes;
        }

        $data['bumdes'] = $bumdes_processed;

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Tambah BUMDes';
        $data['content'] = 'master/bumdes/add';
        $data['workunits'] = $this->workunits->order_by('a.workunitname', 'ASC')->result();
        $data['provinces'] = $this->msprovinces->order_by('name', 'ASC')->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $ownername = getPost('ownername');
        $businessname = getPost('businessname');
        $workunit = getPost('workunit');
        $address = getPost('address');
        $username = getPost('username');
        $password = getPost('password');
        $province = getPost('province');
        $cities = getPost('cities');
        $districts = getPost('districts');
        $villages = getPost('villages');

        $this->form_validation->set_rules('ownername', 'Nama Pemilik', array('required', 'trim'));
        $this->form_validation->set_rules('businessname', 'Nama Usaha', array('required', 'trim'));
        $this->form_validation->set_rules('workunit[]', 'Unit Kerja', array('required'));
        $this->form_validation->set_rules('address', 'Alamat', array('required', 'trim'));
        $this->form_validation->set_rules('province', 'Provinsi', array('required', 'trim'));
        $this->form_validation->set_rules('cities', 'Kabupaten/Kota', array('required', 'trim'));
        $this->form_validation->set_rules('districts', 'Kecamatan', array('required', 'trim'));
        $this->form_validation->set_rules('villages', 'Desa', array('required', 'trim'));

        if ($this->form_validation->run() === false) {
            return JSONResponseDefault('FAILED', strip_tags(validation_errors()));
        }

        $user = $this->superadmins->get(array('username' => $username))->row();

        if ($user != null) {
            return JSONResponseDefault('FAILED', 'Username sudah digunakan.');
        }

        // Process multiple workunit selection
        $workunit_ids = '';
        if (is_array($workunit)) {
            $workunit_ids = implode(',', $workunit);
        } else {
            $workunit_ids = $workunit;
        }

        $insert = array();
        $insert['name'] = $ownername;
        $insert['username'] = $username;
        $insert['password'] = password_hash($password, PASSWORD_DEFAULT);
        $insert['role'] = 'BUMDes';
        $insert['businessname'] = $businessname;
        $insert['address'] = $address;
        $insert['villageid'] = $villages;
        $insert['workunitid'] = $workunit_ids;
        $insert['createddate'] = getCurrentDate();

        $this->superadmins->insert($insert);

        return JSONResponseDefault('OK', 'Data berhasil disimpan.');
    }

    public function process_delete()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $id = getPost('id');

        $get = $this->superadmins->get(array('id' => $id))->row();

        if ($get == null) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan.');
        }

        $this->superadmins->delete(array('id' => $id));

        return JSONResponseDefault('OK', 'Data berhasil dihapus.');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $get = $this->superadmins->select('a.*, e.code as province_code, d.code as city_code, c.code as district_code, b.code as village_code')
            ->join('msvillages b', 'b.code = CAST(a.villageid AS UNSIGNED)')
            ->join('msdistricts c', 'b.district_code = c.code')
            ->join('mscities d', 'c.city_code = d.code')
            ->join('msprovinces e', 'd.province_code = e.code')
            ->get(array(
                'a.id' => $id
            ))->row();

        if ($get == null) {
            return redirect(base_url('master/bumdes'));
        }

        $data = array();
        $data['title'] = 'Ubah BUMDes';
        $data['content'] = 'master/bumdes/edit';
        $data['workunits'] = $this->workunits->order_by('a.workunitname', 'ASC')->result();
        $data['provinces'] = $this->msprovinces->order_by('name', 'ASC')->result();
        $data['bumdes'] = $get;

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $ownername = getPost('ownername');
        $businessname = getPost('businessname');
        $workunit = getPost('workunit');
        $address = getPost('address');
        $username = getPost('username');
        $password = getPost('password');
        $province = getPost('province');
        $cities = getPost('cities');
        $districts = getPost('districts');
        $villages = getPost('villages');

        $this->form_validation->set_rules('ownername', 'Nama Pemilik', array('required', 'trim'));
        $this->form_validation->set_rules('businessname', 'Nama Usaha', array('required', 'trim'));
        $this->form_validation->set_rules('workunit[]', 'Unit Kerja', array('required'));
        $this->form_validation->set_rules('address', 'Alamat', array('required', 'trim'));
        $this->form_validation->set_rules('province', 'Provinsi', array('required', 'trim'));
        $this->form_validation->set_rules('cities', 'Kabupaten/Kota', array('required', 'trim'));
        $this->form_validation->set_rules('districts', 'Kecamatan', array('required', 'trim'));
        $this->form_validation->set_rules('villages', 'Desa', array('required', 'trim'));

        if ($this->form_validation->run() === false) {
            return JSONResponseDefault('FAILED', strip_tags(validation_errors()));
        }

        $user = $this->superadmins->get(array('username' => $username))->row();

        if ($user != null && $user->id != $id) {
            return JSONResponseDefault('FAILED', 'Username sudah digunakan.');
        }

        // Process multiple workunit selection
        $workunit_ids = '';
        if (is_array($workunit)) {
            $workunit_ids = implode(',', $workunit);
        } else {
            $workunit_ids = $workunit;
        }

        $update = array();
        $update['name'] = $ownername;
        $update['username'] = $username;
        $update['role'] = 'BUMDes';
        $update['businessname'] = $businessname;
        $update['address'] = $address;
        $update['villageid'] = $villages;
        $update['workunitid'] = $workunit_ids;

        if (!empty($password)) {
            $update['password'] = password_hash($password, PASSWORD_DEFAULT);
        }

        $this->superadmins->update(array('id' => $id), $update);

        return JSONResponseDefault('OK', 'Data berhasil disimpan.');
    }

    public function verify()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        if (!isSuperAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $id = getPost('id');
        $status = getPost('status');

        // Validasi input
        $this->form_validation->set_rules('id', 'ID', 'required|numeric');
        $this->form_validation->set_rules('status', 'Status', 'required|in_list[Aktif,Nonaktif]');

        if ($this->form_validation->run() === false) {
            return JSONResponseDefault('FAILED', strip_tags(validation_errors()));
        }

        // Cek apakah BUMDes ada
        $bumdes = $this->superadmins->get(array('id' => $id, 'role' => 'BUMDes'))->row();
        if (!$bumdes) {
            return JSONResponseDefault('FAILED', 'Data BUMDes tidak ditemukan.');
        }

        // Update status
        $update_data = array(
            'status' => $status,
            'updatedby' => getCurrentIdUser(),
            'updateddate' => getCurrentDate()
        );

        $this->superadmins->update(array('id' => $id), $update_data);

        $message = $status === 'Aktif' ? 'BUMDes berhasil diverifikasi dan diaktifkan.' : 'BUMDes berhasil dinonaktifkan.';

        return JSONResponseDefault('OK', $message);
    }
}
