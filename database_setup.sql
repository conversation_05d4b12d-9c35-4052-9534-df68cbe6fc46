-- SQL untuk membuat tabel transaction_status_history dan menambahkan kolom status ke msusers

-- 1. Buat tabel transaction_status_history
CREATE TABLE IF NOT EXISTS `transaction_status_history` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `transaction_id` int(11) unsigned NOT NULL,
  `old_status` varchar(50) DEFAULT NULL,
  `new_status` varchar(50) NOT NULL,
  `changed_by` int(11) unsigned NOT NULL,
  `changed_date` datetime NOT NULL,
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  <PERSON>EY `transaction_id` (`transaction_id`),
  KEY `changed_by` (`changed_by`),
  <PERSON>EY `changed_date` (`changed_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2. Tambahkan foreign key constraints untuk transaction_status_history
ALTER TABLE `transaction_status_history` 
ADD CONSTRAINT `fk_transaction_status_history_transaction` 
FOREIGN KEY (`transaction_id`) REFERENCES `transactions`(`id`) ON DELETE CASCADE;

ALTER TABLE `transaction_status_history` 
ADD CONSTRAINT `fk_transaction_status_history_user` 
FOREIGN KEY (`changed_by`) REFERENCES `msusers`(`id`) ON DELETE CASCADE;

-- 3. Tambahkan kolom status ke tabel msusers jika belum ada
ALTER TABLE `msusers` 
ADD COLUMN `status` ENUM('Pending', 'Aktif', 'Nonaktif') NOT NULL DEFAULT 'Aktif' AFTER `role`;

-- 4. Update existing BUMDes users yang dibuat melalui self-registration (createdby = 0) ke status Pending
UPDATE `msusers` 
SET `status` = 'Pending' 
WHERE `role` = 'BUMDes' AND `createdby` = 0;

-- 5. Buat tabel migrations jika belum ada (untuk tracking migration)
CREATE TABLE IF NOT EXISTS `migrations` (
  `version` bigint(20) NOT NULL,
  PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 6. Insert migration version
INSERT IGNORE INTO `migrations` (`version`) VALUES (20250109000001);
INSERT IGNORE INTO `migrations` (`version`) VALUES (20250109000002);
