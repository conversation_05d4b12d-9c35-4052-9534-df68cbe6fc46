<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Dashboard Pengguna BUMDes</h6>
    <div class="text-end">
        <p class="mb-0 text-secondary-light">Unit Usaha: <strong><?= $workunit_name ?></strong></p>
        <p class="mb-0 text-secondary-light">BUMDes: <strong><?= $bumdes_name ?></strong></p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row row-cols-xl-3 row-cols-lg-3 row-cols-sm-2 row-cols-1 gy-4 mb-24">
    <div class="col">
        <div class="card shadow-none border bg-gradient-start-1 h-100">
            <div class="card-body p-20">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                    <div>
                        <p class="fw-medium text-primary-light mb-1">Total Pendapatan</p>
                        <h6 class="mb-0">Rp <?= number_format($total_income, 0, ',', '.') ?></h6>
                    </div>
                    <div class="w-50-px h-50-px bg-cyan rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:dollar-minimalistic-bold" class="text-white text-2xl mb-0"></iconify-icon>
                    </div>
                </div>
                <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                    <span class="d-inline-flex align-items-center gap-1 text-success-main">
                        <iconify-icon icon="bxs:up-arrow" class="text-xs"></iconify-icon> Tahun <?= getSelectedYear() ?>
                    </span>
                </p>
            </div>
        </div>
    </div>

    <div class="col">
        <div class="card shadow-none border bg-gradient-start-2 h-100">
            <div class="card-body p-20">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                    <div>
                        <p class="fw-medium text-primary-light mb-1">Total Pengeluaran</p>
                        <h6 class="mb-0">Rp <?= number_format($total_expense, 0, ',', '.') ?></h6>
                    </div>
                    <div class="w-50-px h-50-px bg-red rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:card-send-bold" class="text-white text-2xl mb-0"></iconify-icon>
                    </div>
                </div>
                <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                    <span class="d-inline-flex align-items-center gap-1 text-danger-main">
                        <iconify-icon icon="bxs:down-arrow" class="text-xs"></iconify-icon> Tahun <?= getSelectedYear() ?>
                    </span>
                </p>
            </div>
        </div>
    </div>

    <div class="col">
        <div class="card shadow-none border bg-gradient-start-3 h-100">
            <div class="card-body p-20">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                    <div>
                        <p class="fw-medium text-primary-light mb-1">Total Transaksi</p>
                        <h6 class="mb-0"><?= $total_transactions ?></h6>
                    </div>
                    <div class="w-50-px h-50-px bg-success-main rounded-circle d-flex justify-content-center align-items-center">
                        <iconify-icon icon="solar:clipboard-list-bold" class="text-white text-2xl mb-0"></iconify-icon>
                    </div>
                </div>
                <p class="fw-medium text-sm text-primary-light mt-12 mb-0 d-flex align-items-center gap-2">
                    <span class="d-inline-flex align-items-center gap-1 text-success-main">
                        <iconify-icon icon="bxs:up-arrow" class="text-xs"></iconify-icon> Tahun <?= getSelectedYear() ?>
                    </span>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Total Modal Card for BUMDes User -->
<?php if (isset($total_modal)): ?>
    <div class="row mb-24">
        <div class="col-md-12">
            <div class="card h-100 radius-8 border bg-gradient-start-2">
                <div class="card-body p-24">
                    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                        <div>
                            <p class="fw-medium text-primary-light mb-1">Total Modal Investasi BUMDes</p>
                            <h4 class="mb-0 text-primary-600">Rp <?= number_format($total_modal, 0, ',', '.') ?></h4>
                            <p class="fw-medium text-sm text-secondary-light mt-8 mb-0">
                                Berdasarkan saldo awal tahun <?= getSelectedYear() ?>
                            </p>
                        </div>
                        <div class="w-60-px h-60-px bg-primary-600 rounded-circle d-flex justify-content-center align-items-center">
                            <iconify-icon icon="solar:wallet-money-bold" class="text-white text-3xl mb-0"></iconify-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Chart Section -->
<div class="row gy-4 mb-24">
    <div class="col-xxl-8 col-xl-12">
        <div class="card h-100">
            <div class="card-body p-24">
                <div class="d-flex align-items-center flex-wrap gap-2 justify-content-between mb-20">
                    <h6 class="mb-0 fw-semibold text-lg">Grafik Keuangan Bulanan</h6>
                    <div class="d-flex align-items-center gap-2">
                        <span class="d-flex align-items-center gap-1 text-success-main fw-medium text-sm">
                            <span class="w-8-px h-8-px bg-success-main rounded-circle"></span>
                            Pendapatan
                        </span>
                        <span class="d-flex align-items-center gap-1 text-danger-main fw-medium text-sm">
                            <span class="w-8-px h-8-px bg-danger-main rounded-circle"></span>
                            Pengeluaran
                        </span>
                    </div>
                </div>

                <div id="chart" class="margin-16-minus"></div>
            </div>
        </div>
    </div>

    <div class="col-xxl-4 col-xl-12">
        <div class="card h-100">
            <div class="card-body p-24">
                <div class="d-flex align-items-center flex-wrap gap-2 justify-content-between mb-20">
                    <h6 class="mb-0 fw-semibold text-lg">Laba Bersih</h6>
                </div>

                <div class="text-center">
                    <div class="w-80-px h-80-px bg-primary-50 rounded-circle d-flex justify-content-center align-items-center mx-auto mb-16">
                        <iconify-icon icon="solar:chart-2-bold" class="text-primary-600 text-3xl"></iconify-icon>
                    </div>
                    <h4 class="mb-8">Rp <?= number_format($total_income - $total_expense, 0, ',', '.') ?></h4>
                    <p class="text-secondary-light mb-0">Laba bersih tahun <?= getSelectedYear() ?></p>

                    <?php if (($total_income - $total_expense) >= 0): ?>
                        <span class="bg-success-focus text-success-main px-16 py-6 rounded-pill fw-medium text-sm mt-16 d-inline-block">
                            <iconify-icon icon="bxs:up-arrow" class="text-xs"></iconify-icon>
                            Profit
                        </span>
                    <?php else: ?>
                        <span class="bg-danger-focus text-danger-main px-16 py-6 rounded-pill fw-medium text-sm mt-16 d-inline-block">
                            <iconify-icon icon="bxs:down-arrow" class="text-xs"></iconify-icon>
                            Loss
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="row gy-4">
    <div class="col-12">
        <div class="card h-100">
            <div class="card-body p-24">
                <div class="d-flex align-items-center flex-wrap gap-2 justify-content-between mb-20">
                    <h6 class="mb-0 fw-semibold text-lg">Transaksi Terbaru</h6>
                    <a href="<?= base_url('master/transaction') ?>" class="text-primary-600 hover-text-primary d-flex align-items-center gap-1">
                        Lihat Semua
                        <iconify-icon icon="solar:alt-arrow-right-linear" class="icon"></iconify-icon>
                    </a>
                </div>

                <div class="table-responsive scroll-sm">
                    <table class="table bordered-table sm-table mb-0">
                        <thead>
                            <tr>
                                <th scope="col">Tanggal</th>
                                <th scope="col">Deskripsi</th>
                                <th scope="col">Jenis</th>
                                <th scope="col">Unit Usaha</th>
                                <th scope="col">Nominal</th>
                                <th scope="col">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($transactions)): ?>
                                <?php foreach ($transactions as $transaction): ?>
                                    <tr>
                                        <td>
                                            <span class="text-md fw-medium text-secondary-light"><?= date('d/m/Y', strtotime($transaction->transactiondate)) ?></span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="flex-grow-1">
                                                    <h6 class="text-md mb-0 fw-medium"><?= $transaction->transactionnote ?></h6>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($transaction->transactiontype == 'Pendapatan'): ?>
                                                <span class="bg-success-focus text-success-main px-16 py-6 rounded-pill fw-medium text-sm">Pendapatan</span>
                                            <?php else: ?>
                                                <span class="bg-danger-focus text-danger-main px-16 py-6 rounded-pill fw-medium text-sm">Pengeluaran</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="text-sm text-secondary-light"><?= isset($transaction->workunitdata) ? $transaction->workunitdata : 'N/A' ?></span>
                                        </td>
                                        <td>
                                            <span class="text-md fw-medium">Rp <?= number_format($transaction->amount, 0, ',', '.') ?></span>
                                        </td>
                                        <td>
                                            <?php if ($transaction->status == 'Lunas'): ?>
                                                <span class="bg-success-focus text-success-main px-16 py-6 rounded-pill fw-medium text-sm">Lunas</span>
                                            <?php else: ?>
                                                <span class="bg-warning-focus text-warning-main px-16 py-6 rounded-pill fw-medium text-sm"><?= $transaction->status ?></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center text-secondary-light">Belum ada transaksi</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Flag to prevent multiple chart initialization
    var bumdesUserChartInitialized = false;

    // Function to initialize BUMDes user dashboard chart
    function initializeBumdesUserChart() {
        // Prevent multiple initialization
        if (bumdesUserChartInitialized) {
            console.log('BUMDes user chart already initialized, skipping...');
            return;
        }
        var options = {
            series: [{
                name: 'Pendapatan',
                data: <?= $chart_revenue ?>
            }, {
                name: 'Pengeluaran',
                data: <?= $chart_expense ?>
            }],
            chart: {
                type: 'area',
                width: '100%',
                height: 350,
                sparkline: {
                    enabled: false
                },
                toolbar: {
                    show: false
                }
            },
            colors: ['#22c55e', '#ef4444'],
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth',
                width: 2
            },
            grid: {
                show: true,
                borderColor: '#e5e7eb',
                strokeDashArray: 3,
                position: 'back'
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.4,
                    opacityTo: 0.1,
                    stops: [0, 90, 100]
                }
            },
            xaxis: {
                categories: <?= $months ?>,
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                }
            },
            yaxis: {
                labels: {
                    formatter: function(val) {
                        return "Rp " + val.toLocaleString('id-ID');
                    }
                }
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return "Rp " + val.toLocaleString('id-ID');
                    }
                }
            }
        };

        // Check if chart element exists before creating chart
        var chartElement = document.querySelector("#chart");
        if (chartElement) {
            // Check if ApexCharts is available
            if (typeof ApexCharts !== 'undefined') {
                var chart = new ApexCharts(chartElement, options);
                chart.render();
                bumdesUserChartInitialized = true; // Mark as initialized
                console.log('BUMDes user chart initialized successfully');
            } else {
                // Queue for later execution if ApexCharts not loaded yet
                console.log('ApexCharts not loaded yet, queuing BUMDes user chart');
                window.ApexChartsQueue = window.ApexChartsQueue || [];
                window.ApexChartsQueue.push({
                    element: chartElement,
                    options: options,
                    timestamp: Date.now()
                });
                bumdesUserChartInitialized = true; // Mark as initialized to prevent multiple queuing
            }
        } else {
            console.warn('Chart element #chart not found');
        }
    }

    // Wait for DOM and scripts to be ready
    window.onload = function() {
        setTimeout(function() {
            initializeBumdesUserChart();
        }, 200);
    };

    // Fallback: Also try when document is ready (with jQuery check)
    function initBumdesWithJQuery() {
        if (typeof $ !== 'undefined') {
            $(document).ready(function() {
                setTimeout(function() {
                    // Only initialize if not already done
                    if (!bumdesUserChartInitialized) {
                        initializeBumdesUserChart();
                    }
                }, 300);
            });
        } else {
            // jQuery not available yet, try again later
            setTimeout(initBumdesWithJQuery, 200);
        }
    }

    // Start jQuery initialization
    setTimeout(initBumdesWithJQuery, 100);

    // Additional fallback for ApexCharts loading
    document.addEventListener('DOMContentLoaded', function() {
        // Check periodically if ApexCharts is loaded
        var checkApexCharts = setInterval(function() {
            if (typeof ApexCharts !== 'undefined') {
                clearInterval(checkApexCharts);
                setTimeout(function() {
                    // Only initialize if not already done
                    if (!bumdesUserChartInitialized) {
                        initializeBumdesUserChart();
                    }
                }, 100);
            }
        }, 100);

        // Stop checking after 10 seconds
        setTimeout(function() {
            clearInterval(checkApexCharts);
        }, 10000);
    });
</script>