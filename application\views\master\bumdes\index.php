<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <h6 class="fw-semibold mb-0">Daftar BUMDes</h6>

    <ul class="d-flex align-items-center gap-2">
        <li class="fw-medium">
            <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                Dashboard
            </a>
        </li>
        <li>-</li>
        <li class="fw-medium">Daftar BUMDes</li>
    </ul>
</div>

<div class="card basic-data-table">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h5 class="card-title mb-0">Daftar BUMDes</h5>
        </div>

        <div>
            <a href="<?= base_url(uri_string() . '/add') ?>" class="btn btn-primary btn-sm">
                <i class="ri-add-line"></i>
                <span>Tambah</span>
            </a>
        </div>
    </div>

    <div class="card-body">
        <div class="table-responsive">
            <table class="table bordered-table datatables">
                <thead>
                    <tr>
                        <th>Nama Pemilik Usaha</th>
                        <th>Nama Usaha</th>
                        <th>Alamat</th>
                        <th>Desa</th>
                        <th>Kecamatan</th>
                        <th>Kabupaten</th>
                        <th>Provinsi</th>
                        <th>Unit Kerja</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </thead>

                <tbody>
                    <?php foreach ($bumdes as $key => $value): ?>
                        <tr>
                            <td><?= $value->name ?></td>
                            <td><?= $value->businessname ?></td>
                            <td><?= $value->address ?></td>
                            <td><?= $value->village ?></td>
                            <td><?= $value->district ?></td>
                            <td><?= $value->city ?></td>
                            <td><?= $value->province ?></td>
                            <td><?= $value->workunit_display ?></td>
                            <td>
                                <?php
                                $status = isset($value->status) ? $value->status : 'Aktif';
                                if ($status === 'Pending'): ?>
                                    <span class="bg-warning-focus text-warning-main px-16 py-4 rounded-pill fw-medium text-sm">Menunggu Verifikasi</span>
                                <?php elseif ($status === 'Aktif'): ?>
                                    <span class="bg-success-focus text-success-main px-16 py-4 rounded-pill fw-medium text-sm">Aktif</span>
                                <?php else: ?>
                                    <span class="bg-danger-focus text-danger-main px-16 py-4 rounded-pill fw-medium text-sm">Nonaktif</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if (isset($value->status) && $value->status === 'Pending'): ?>
                                    <button type="button" class="btn btn-success btn-sm me-1" onclick="verifyBumdes(<?= $value->id ?>, 'Aktif', '<?= $value->businessname ?>')">
                                        <i class="ri-check-line"></i>
                                        Verifikasi
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm me-1" onclick="verifyBumdes(<?= $value->id ?>, 'Nonaktif', '<?= $value->businessname ?>')">
                                        <i class="ri-close-line"></i>
                                        Tolak
                                    </button>
                                <?php else: ?>
                                    <?php if (isset($value->status) && $value->status === 'Aktif'): ?>
                                        <button type="button" class="btn btn-warning btn-sm me-1" onclick="verifyBumdes(<?= $value->id ?>, 'Nonaktif', '<?= $value->businessname ?>')">
                                            <i class="ri-pause-line"></i>
                                            Nonaktifkan
                                        </button>
                                    <?php else: ?>
                                        <button type="button" class="btn btn-success btn-sm me-1" onclick="verifyBumdes(<?= $value->id ?>, 'Aktif', '<?= $value->businessname ?>')">
                                            <i class="ri-play-line"></i>
                                            Aktifkan
                                        </button>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <a href="<?= base_url(uri_string() . '/edit/' . $value->id) ?>" class="btn btn-primary btn-sm me-1">
                                    <i class="ri-pencil-line"></i>
                                </a>

                                <button type="button" class="btn btn-danger btn-sm" onclick="deleteData('<?= $value->businessname ?>', <?= $value->id ?>)">
                                    <i class="ri-delete-bin-line"></i>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    function verifyBumdes(id, status, businessName) {
        let title = '';
        let text = '';
        let confirmButtonText = '';

        if (status === 'Aktif') {
            title = 'Verifikasi BUMDes';
            text = `Apakah Anda yakin ingin memverifikasi dan mengaktifkan BUMDes "${businessName}"?`;
            confirmButtonText = 'Ya, Verifikasi';
        } else {
            title = 'Nonaktifkan BUMDes';
            text = `Apakah Anda yakin ingin menonaktifkan BUMDes "${businessName}"?`;
            confirmButtonText = 'Ya, Nonaktifkan';
        }

        Swal.fire({
            title: title,
            text: text,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: status === 'Aktif' ? '#28a745' : '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: confirmButtonText,
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url('master/bumdes/verify') ?>',
                    type: 'POST',
                    data: {
                        id: id,
                        status: status
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.RESULT === 'OK') {
                            Swal.fire({
                                title: 'Berhasil!',
                                text: response.MESSAGE,
                                icon: 'success',
                                confirmButtonColor: '#28a745'
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: response.MESSAGE,
                                icon: 'error',
                                confirmButtonColor: '#dc3545'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat memproses permintaan',
                            icon: 'error',
                            confirmButtonColor: '#dc3545'
                        });
                    }
                });
            }
        });
    }
</script>