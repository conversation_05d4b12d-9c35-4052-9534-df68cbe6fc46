<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
    <div>
        <h6 class="fw-semibold mb-0">Laporan <PERSON>aksi B<PERSON></h6>
        <p class="mb-0 text-secondary-light">Periode: <?= date('F Y', strtotime($month)) ?></p>
    </div>

    <div class="d-flex align-items-center gap-3">
        <a href="<?= base_url('report/monthly_pdf/' . $month) ?>" class="btn btn-danger btn-sm d-flex gap-1 align-items-center" target="_blank">
            <iconify-icon icon="solar:download-bold"></iconify-icon>
            <span>Export PDF</span>
        </a>

        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="<?= base_url() ?>" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Laporan Transaksi Bulanan</li>
        </ul>
    </div>
</div>

<div class="row gy-4">
    <div class="col-xxl-3 col-sm-6">
        <div class="card px-24 py-16 shadow-none radius-8 border h-100 bg-gradient-start-4">
            <div class="card-body p-0">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-1 mb-8">
                    <div class="d-flex align-items-center">
                        <div class="w-64-px h-64-px radius-16 bg-base-50 d-flex justify-content-center align-items-center me-20">
                            <span class="mb-0 w-40-px h-40-px bg-success-600 flex-shrink-0 text-white d-flex justify-content-center align-items-center radius-8 h6 mb-0">
                                <iconify-icon icon="flowbite:chart-mixed-dollar-outline" class="icon"></iconify-icon>
                            </span>
                        </div>

                        <div>
                            <span class="mb-1 fw-medium text-secondary-light text-md">Total Pendapatan</span>
                            <h6 class="fw-semibold mb-0">
                                Rp <?= IDR($total_income) ?>
                            </h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xxl-3 col-sm-6">
        <div class="card px-24 py-16 shadow-none radius-8 border h-100 bg-gradient-start-5">
            <div class="card-body p-0">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-1 mb-8">
                    <div class="d-flex align-items-center">
                        <div class="w-64-px h-64-px radius-16 bg-base-50 d-flex justify-content-center align-items-center me-20">
                            <span class="mb-0 w-40-px h-40-px bg-danger-600 flex-shrink-0 text-white d-flex justify-content-center align-items-center radius-8 h6 mb-0">
                                <iconify-icon icon="flowbite:chart-mixed-dollar-outline" class="icon"></iconify-icon>
                            </span>
                        </div>

                        <div>
                            <span class="mb-1 fw-medium text-secondary-light text-md">Total Pengeluaran</span>
                            <h6 class="fw-semibold mb-0">
                                Rp <?= IDR($total_expense) ?>
                            </h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-12">
        <div class="card basic-data-table">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">Daftar Laporan Transaksi Bulanan</h5>
                </div>

                <div class="w-25">
                    <label for="month" class="form-label">Bulan</label>
                    <input type="month" name="month" id="month" class="form-control" value="<?= $month ?>">
                </div>
            </div>

            <div class="card-body">
                <div class="table-responsive">
                    <table class="table bordered-table datatables">
                        <thead>
                            <tr>
                                <th>Tanggal Transaksi</th>
                                <th>Total Pendapatan</th>
                                <th>Total Pengeluaran</th>
                            </tr>
                        </thead>

                        <tbody>
                            <?php for ($i = 1; $i <= date('t', strtotime($month)); $i++): ?>
                                <tr>
                                    <td><?= tgl_indo(date('Y-m-d', strtotime($month . '-' . $i))) ?></td>
                                    <td>Rp <?php
                                            if (isBumdesUser()) {
                                                echo number_format($transactions->sum('amount', array(
                                                    'DATE(transactiondate)' => date('Y-m-d', strtotime($month . '-' . $i)),
                                                    'transactiontype' => 'Pendapatan',
                                                    'workunitid' => getWorkunitId(),
                                                    'createdby' => getBumdesId(),
                                                )) ?? 0, 0, ',', '.');
                                            } else {
                                                // BUMDes biasa - include data from village BUMDes users
                                                $village_id = getVillageId();
                                                $current_user_id = getCurrentIdUser();

                                                if ($village_id) {
                                                    // Get all BUMDes users from the same village
                                                    $CI = &get_instance();
                                                    $CI->load->model('Bumdes_user_model', 'bumdes_users');
                                                    $village_bumdes_users = array();
                                                    $bumdes_users_in_village = $CI->bumdes_users->select('a.id')
                                                        ->join('msusers b', 'a.bumdes_id = b.id', 'inner')
                                                        ->result(array('b.villageid' => $village_id));

                                                    foreach ($bumdes_users_in_village as $user) {
                                                        $village_bumdes_users[] = $user->id;
                                                    }

                                                    // Include current BUMDes and all BUMDes users from same village
                                                    $allowed_creators = array_merge(array($current_user_id), $village_bumdes_users);

                                                    // Calculate using custom query
                                                    $income_query = $CI->db->select('SUM(amount) as total')
                                                        ->from('transactions')
                                                        ->where('DATE(transactiondate)', date('Y-m-d', strtotime($month . '-' . $i)))
                                                        ->where('transactiontype', 'Pendapatan')
                                                        ->where_in('createdby', $allowed_creators)
                                                        ->get();
                                                    echo number_format($income_query->row()->total ?? 0, 0, ',', '.');
                                                } else {
                                                    echo number_format($transactions->sum('amount', array(
                                                        'DATE(transactiondate)' => date('Y-m-d', strtotime($month . '-' . $i)),
                                                        'transactiontype' => 'Pendapatan',
                                                        'createdby' => getCurrentIdUser(),
                                                    )) ?? 0, 0, ',', '.');
                                                }
                                            }
                                            ?></td>
                                    <td>Rp <?php
                                            if (isBumdesUser()) {
                                                echo number_format($transactions->sum('amount', array(
                                                    'DATE(transactiondate)' => date('Y-m-d', strtotime($month . '-' . $i)),
                                                    'transactiontype' => 'Pengeluaran',
                                                    'workunitid' => getWorkunitId(),
                                                    'createdby' => getBumdesId(),
                                                )) ?? 0, 0, ',', '.');
                                            } else {
                                                // BUMDes biasa - include data from village BUMDes users
                                                $village_id = getVillageId();
                                                $current_user_id = getCurrentIdUser();

                                                if ($village_id) {
                                                    // Get all BUMDes users from the same village
                                                    $CI = &get_instance();
                                                    $CI->load->model('Bumdes_user_model', 'bumdes_users');
                                                    $village_bumdes_users = array();
                                                    $bumdes_users_in_village = $CI->bumdes_users->select('a.id')
                                                        ->join('msusers b', 'a.bumdes_id = b.id', 'inner')
                                                        ->result(array('b.villageid' => $village_id));

                                                    foreach ($bumdes_users_in_village as $user) {
                                                        $village_bumdes_users[] = $user->id;
                                                    }

                                                    // Include current BUMDes and all BUMDes users from same village
                                                    $allowed_creators = array_merge(array($current_user_id), $village_bumdes_users);

                                                    // Calculate using custom query
                                                    $expense_query = $CI->db->select('SUM(amount) as total')
                                                        ->from('transactions')
                                                        ->where('DATE(transactiondate)', date('Y-m-d', strtotime($month . '-' . $i)))
                                                        ->where('transactiontype', 'Pengeluaran')
                                                        ->where_in('createdby', $allowed_creators)
                                                        ->get();
                                                    echo number_format($expense_query->row()->total ?? 0, 0, ',', '.');
                                                } else {
                                                    echo number_format($transactions->sum('amount', array(
                                                        'DATE(transactiondate)' => date('Y-m-d', strtotime($month . '-' . $i)),
                                                        'transactiontype' => 'Pengeluaran',
                                                        'createdby' => getCurrentIdUser(),
                                                    )) ?? 0, 0, ',', '.');
                                                }
                                            }
                                            ?></td>
                                </tr>
                            <?php endfor; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Detail Transaksi Bulanan -->
    <div class="col-md-12 mt-24">
        <div class="card basic-data-table">
            <div class="card-header">
                <h5 class="card-title mb-0">Detail Transaksi Bulanan</h5>
            </div>

            <div class="card-body">
                <!-- Filter Section -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="filter_type" class="form-label">Filter Tipe Transaksi</label>
                        <select id="filter_type" class="form-select">
                            <option value="">Semua Tipe</option>
                            <option value="Pendapatan">Pendapatan</option>
                            <option value="Pengeluaran">Pengeluaran</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="filter_workunit" class="form-label">Filter Unit Kerja</label>
                        <select id="filter_workunit" class="form-select">
                            <option value="">Semua Unit Kerja</option>
                            <?php
                            // Get unique work units from the results
                            $unique_workunits = array();
                            foreach ($monthly_transactions as $item) {
                                if (!isset($unique_workunits[$item->workunitdata]) && $item->workunitdata != 'N/A') {
                                    $unique_workunits[$item->workunitdata] = $item->workunitdata;
                                }
                            }
                            foreach ($unique_workunits as $workunit): ?>
                                <option value="<?= $workunit ?>"><?= $workunit ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="filter_status" class="form-label">Filter Status</label>
                        <select id="filter_status" class="form-select">
                            <option value="">Semua Status</option>
                            <option value="Lunas">Lunas</option>
                            <option value="Menunggu Pembayaran">Menunggu Pembayaran</option>
                            <option value="Dibatalkan">Dibatalkan</option>
                        </select>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table bordered-table datatables">
                        <thead>
                            <tr>
                                <th>Kode Transaksi</th>
                                <th>Tanggal Transaksi</th>
                                <th>Catatan Transaksi</th>
                                <th>Tipe Transaksi</th>
                                <th>Unit Kerja</th>
                                <th>Status</th>
                                <th>Nominal</th>
                            </tr>
                        </thead>

                        <tbody>
                            <?php foreach ($monthly_transactions as $key => $value): ?>
                                <tr>
                                    <td><?= $value->transactioncode ?></td>
                                    <td><?= tgl_indo($value->transactiondate) ?></td>
                                    <td><?= $value->transactionnote ?></td>
                                    <td>
                                        <?php if ($value->transactiontype == 'Pendapatan'): ?>
                                            <span class="badge bg-success-600 text-white text-sm fw-semibold px-12 py-4 radius-4">Pendapatan</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger-600 text-white text-sm fw-semibold px-12 py-4 radius-4">Pengeluaran</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $value->workunitdata ?></td>
                                    <td>
                                        <?php if ($value->status == 'Lunas'): ?>
                                            <span class="badge bg-success-600 text-white text-sm fw-semibold px-12 py-4 radius-4">Lunas</span>
                                        <?php elseif ($value->status == 'Menunggu Pembayaran'): ?>
                                            <span class="badge bg-warning-600 text-white text-sm fw-semibold px-12 py-4 radius-4">Menunggu Pembayaran</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger-600 text-white text-sm fw-semibold px-12 py-4 radius-4">Dibatalkan</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="fw-semibold text-primary-600">Rp <?= number_format($value->amount, 0, ',', '.') ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('#month').change(function() {
            window.location.href = '<?= base_url('report/monthly') ?>' + '?month=' + $(this).val();
        });

        // Initialize DataTable for detail transactions
        var table = $('.datatables').last().DataTable();

        // Transaction type filter
        $('#filter_type').on('change', function() {
            var type = this.value;
            if (type) {
                // Filter by transaction type (column index 3)
                table.column(3).search(type, false, false).draw();
            } else {
                table.column(3).search('').draw();
            }
        });

        // Work unit filter
        $('#filter_workunit').on('change', function() {
            var workunit = this.value;
            if (workunit) {
                // Filter by work unit (column index 4)
                table.column(4).search(workunit, false, false).draw();
            } else {
                table.column(4).search('').draw();
            }
        });

        // Status filter
        $('#filter_status').on('change', function() {
            var status = this.value;
            if (status) {
                // Filter by status (column index 5)
                table.column(5).search(status, false, false).draw();
            } else {
                table.column(5).search('').draw();
            }
        });
    };
</script>