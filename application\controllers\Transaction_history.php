<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Transaction_history extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Transaction_status_history', 'status_history');
        $this->load->model('Transactions', 'transactions');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'History Perubahan Status Transaksi';
        $data['content'] = 'transaction_history/index';

        // Get filter parameters
        $date_from = getPost('date_from') ?: date('Y-m-01'); // Default to start of current month
        $date_to = getPost('date_to') ?: date('Y-m-d'); // Default to today
        $status = getPost('status');
        $transaction_type = getPost('transaction_type');

        // Prepare filters
        $filters = array(
            'user_role' => getSessionValue('ROLE'),
            'date_from' => $date_from,
            'date_to' => $date_to
        );

        if (!empty($status)) {
            $filters['status'] = $status;
        }

        if (!empty($transaction_type)) {
            $filters['transaction_type'] = $transaction_type;
        }

        // Get history data
        $data['history'] = $this->status_history->getHistoryWithFilters($filters, 50)->result();
        $data['statistics'] = $this->status_history->getStatusChangeStatistics($filters);

        // Filter values for form
        $data['date_from'] = $date_from;
        $data['date_to'] = $date_to;
        $data['status'] = $status;
        $data['transaction_type'] = $transaction_type;

        return $this->load->view('master', $data);
    }

    public function get_transaction_history()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $transaction_id = getPost('transaction_id');

        if (empty($transaction_id)) {
            return JSONResponseDefault('FAILED', 'ID transaksi tidak valid.');
        }

        // Check if user has access to this transaction
        $transaction = $this->transactions->get(array('id' => $transaction_id))->row();
        if (!$transaction) {
            return JSONResponseDefault('FAILED', 'Transaksi tidak ditemukan.');
        }

        // Check access based on user role
        $current_role = getSessionValue('ROLE');
        $has_access = false;

        if (isSuperAdmin()) {
            $has_access = true;
        } elseif (isBumdes()) {
            // BUMDes can see transactions from their village
            $village_id = getVillageId();
            if ($village_id) {
                // Check if transaction creator is from same village
                $creator = $this->db->get_where('msusers', array('id' => $transaction->createdby))->row();
                if ($creator && $creator->villageid == $village_id) {
                    $has_access = true;
                }
                
                // Also check if it's from BUMDes users in same village
                $this->load->model('Bumdes_user_model', 'bumdes_users');
                $bumdes_user = $this->bumdes_users->get(array('id' => $transaction->createdby))->row();
                if ($bumdes_user) {
                    $bumdes_creator = $this->db->get_where('msusers', array('id' => $bumdes_user->bumdes_id))->row();
                    if ($bumdes_creator && $bumdes_creator->villageid == $village_id) {
                        $has_access = true;
                    }
                }
            }
        } elseif (isBumdesUser()) {
            // BUMDes User can only see their own transactions
            if ($transaction->workunitid == getWorkunitId() && $transaction->createdby == getBumdesId()) {
                $has_access = true;
            }
        }

        if (!$has_access) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses ke transaksi ini.');
        }

        // Get history
        $history = $this->status_history->getTransactionHistory($transaction_id);

        return JSONResponse(array(
            'RESULT' => 'OK',
            'MESSAGE' => 'Data berhasil diambil',
            'DATA' => $history
        ));
    }

    public function export()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        // Get filter parameters
        $date_from = getPost('date_from') ?: date('Y-m-01');
        $date_to = getPost('date_to') ?: date('Y-m-d');
        $status = getPost('status');
        $transaction_type = getPost('transaction_type');

        // Prepare filters
        $filters = array(
            'user_role' => getSessionValue('ROLE'),
            'date_from' => $date_from,
            'date_to' => $date_to
        );

        if (!empty($status)) {
            $filters['status'] = $status;
        }

        if (!empty($transaction_type)) {
            $filters['transaction_type'] = $transaction_type;
        }

        // Get all history data for export
        $history = $this->status_history->getHistoryWithFilters($filters)->result();

        // Load library for Excel export
        $this->load->library('excel');
        
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->setActiveSheetIndex(0);
        $sheet = $objPHPExcel->getActiveSheet();

        // Set headers
        $sheet->setCellValue('A1', 'Kode Transaksi');
        $sheet->setCellValue('B1', 'Tanggal Transaksi');
        $sheet->setCellValue('C1', 'Nominal');
        $sheet->setCellValue('D1', 'Status Lama');
        $sheet->setCellValue('E1', 'Status Baru');
        $sheet->setCellValue('F1', 'Diubah Oleh');
        $sheet->setCellValue('G1', 'Role');
        $sheet->setCellValue('H1', 'Tanggal Perubahan');
        $sheet->setCellValue('I1', 'Unit Usaha');
        $sheet->setCellValue('J1', 'Catatan');

        // Fill data
        $row = 2;
        foreach ($history as $item) {
            $sheet->setCellValue('A' . $row, $item->transactioncode);
            $sheet->setCellValue('B' . $row, date('d/m/Y', strtotime($item->transactiondate)));
            $sheet->setCellValue('C' . $row, 'Rp ' . number_format($item->amount, 0, ',', '.'));
            $sheet->setCellValue('D' . $row, $item->old_status ?: '-');
            $sheet->setCellValue('E' . $row, $item->new_status);
            $sheet->setCellValue('F' . $row, $item->changed_by_name);
            $sheet->setCellValue('G' . $row, $item->changed_by_role);
            $sheet->setCellValue('H' . $row, date('d/m/Y H:i', strtotime($item->changed_date)));
            $sheet->setCellValue('I' . $row, $item->workunitname ?: '-');
            $sheet->setCellValue('J' . $row, $item->notes ?: '-');
            $row++;
        }

        // Set filename
        $filename = 'History_Status_Transaksi_' . date('Y-m-d_H-i-s') . '.xlsx';

        // Output
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $objWriter->save('php://output');
    }
}
