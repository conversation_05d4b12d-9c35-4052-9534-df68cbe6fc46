<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Transaction_history extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Transaction_status_history', 'status_history');
        $this->load->model('Transactions', 'transactions');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'History Perubahan Status Transaksi';
        $data['content'] = 'transaction_history/index';

        // Get filter parameters
        $date_from = getPost('date_from') ?: date('Y-m-01'); // Default to start of current month
        $date_to = getPost('date_to') ?: date('Y-m-d'); // Default to today
        $status = getPost('status');
        $transaction_type = getPost('transaction_type');

        // Prepare filters
        $filters = array(
            'user_role' => getSessionValue('ROLE'),
            'date_from' => $date_from,
            'date_to' => $date_to
        );

        if (!empty($status)) {
            $filters['status'] = $status;
        }

        if (!empty($transaction_type)) {
            $filters['transaction_type'] = $transaction_type;
        }

        // Get history data
        $history_result = $this->status_history->getHistoryWithFilters($filters, 50);
        $data['history'] = $history_result ? $history_result->result() : array();
        $data['statistics'] = $this->status_history->getStatusChangeStatistics($filters);

        // Filter values for form
        $data['date_from'] = $date_from;
        $data['date_to'] = $date_to;
        $data['status'] = $status;
        $data['transaction_type'] = $transaction_type;

        return $this->load->view('master', $data);
    }

    public function get_transaction_history()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses.');
        }

        $transaction_id = getPost('transaction_id');

        if (empty($transaction_id)) {
            return JSONResponseDefault('FAILED', 'ID transaksi tidak valid.');
        }

        // Check if user has access to this transaction
        $transaction = $this->transactions->get(array('id' => $transaction_id))->row();
        if (!$transaction) {
            return JSONResponseDefault('FAILED', 'Transaksi tidak ditemukan.');
        }

        // Check access based on user role
        $has_access = false;

        if (isSuperAdmin()) {
            $has_access = true;
        } elseif (isBumdes()) {
            // BUMDes can see transactions from their village
            $village_id = getVillageId();
            if ($village_id) {
                // Check if transaction creator is from same village
                $creator = $this->db->get_where('msusers', array('id' => $transaction->createdby))->row();
                if ($creator && $creator->villageid == $village_id) {
                    $has_access = true;
                }

                // Also check if it's from BUMDes users in same village
                $this->load->model('Bumdes_user_model', 'bumdes_users');
                $bumdes_user = $this->bumdes_users->get(array('id' => $transaction->createdby))->row();
                if ($bumdes_user) {
                    $bumdes_creator = $this->db->get_where('msusers', array('id' => $bumdes_user->bumdes_id))->row();
                    if ($bumdes_creator && $bumdes_creator->villageid == $village_id) {
                        $has_access = true;
                    }
                }
            }
        } elseif (isBumdesUser()) {
            // BUMDes User can only see their own transactions
            if ($transaction->workunitid == getWorkunitId() && $transaction->createdby == getBumdesId()) {
                $has_access = true;
            }
        }

        if (!$has_access) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses ke transaksi ini.');
        }

        // Get history
        $history = $this->status_history->getTransactionHistory($transaction_id);

        return JSONResponse(array(
            'RESULT' => 'OK',
            'MESSAGE' => 'Data berhasil diambil',
            'DATA' => $history
        ));
    }

    public function export()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        // Get filter parameters
        $date_from = getPost('date_from') ?: date('Y-m-01');
        $date_to = getPost('date_to') ?: date('Y-m-d');
        $status = getPost('status');
        $transaction_type = getPost('transaction_type');

        // Prepare filters
        $filters = array(
            'user_role' => getSessionValue('ROLE'),
            'date_from' => $date_from,
            'date_to' => $date_to
        );

        if (!empty($status)) {
            $filters['status'] = $status;
        }

        if (!empty($transaction_type)) {
            $filters['transaction_type'] = $transaction_type;
        }

        // Get all history data for export
        $history_result = $this->status_history->getHistoryWithFilters($filters);
        $history = $history_result ? $history_result->result() : array();

        // Export as CSV instead of Excel for simplicity
        $filename = 'History_Status_Transaksi_' . date('Y-m-d_H-i-s') . '.csv';

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        // Output CSV
        $output = fopen('php://output', 'w');

        // Add BOM for UTF-8
        fprintf($output, chr(0xEF) . chr(0xBB) . chr(0xBF));

        // CSV headers
        fputcsv($output, array(
            'Kode Transaksi',
            'Tanggal Transaksi',
            'Nominal',
            'Status Lama',
            'Status Baru',
            'Diubah Oleh',
            'Role',
            'Tanggal Perubahan',
            'Unit Usaha',
            'Catatan'
        ));

        // CSV data
        foreach ($history as $item) {
            fputcsv($output, array(
                $item->transactioncode,
                date('d/m/Y', strtotime($item->transactiondate)),
                'Rp ' . number_format($item->amount, 0, ',', '.'),
                $item->old_status ?: '-',
                $item->new_status,
                $item->changed_by_name,
                $item->changed_by_role,
                date('d/m/Y H:i', strtotime($item->changed_date)),
                $item->workunitname ?: '-',
                $item->notes ?: '-'
            ));
        }

        fclose($output);
    }
}
